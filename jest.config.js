const config = {
  preset: 'ts-jest',
  setupFiles: ['<rootDir>/__mocks__/envTest.js'],
  setupFilesAfterEnv: ['<rootDir>/__mocks__/jest-setup.js'],
  moduleDirectories: [
    '<rootDir>/src',
    'node_modules',
  ],
  transform: {
    '^.+\\.jsx?$': 'babel-jest',
    '.+\\.(css|scss)$': 'jest-css-modules-transform',
  },
  transformIgnorePatterns: [
    '/node_modules/(?!@nv2/nv2-pkg-js-theme).+\\.js$',
  ],
  moduleNameMapper: {
    '\\.(ico|gif|ttf|eot|svg|png)$': '<rootDir>/__mocks__/fileMock.js',
    '\\.(scss|sass|css)$': 'identity-obj-proxy',
    '^@/(.*)$': '<rootDir>/$1',
    '^core/(.*)$': '<rootDir>/src/core/$1',
    '^features/(.*)$': '<rootDir>/src/features/$1',
    '^shared/(.*)$': '<rootDir>/src/shared/$1',
    '^assets/(.*)$': '<rootDir>/src/assets/$1',
  },
  testEnvironment: 'jsdom',
  reporters: ['default', 'jest-junit'],
  coverageReporters: ['cobertura', 'text', 'lcov'],
};

module.exports = config;
