# Changelog

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

### Unreleased

### Added

### Changed

### Removed

### Fixed

## Released
0.0.19 01-09-2025

### Added

### Changed
[SIM-3658] - updated theme version

### Removed

### Fixed

## Released
0.0.18 21-08-2025


### Added
[SIM-3069] - Add New theme variables in all repo of simplify

### Changed

### Removed

### Fixed

## Released
0.0.17 10-06-2025

### Added
[SIMM-2989] - [FE] Integration of voice/SMS definitions is required

### Changed

### Removed

### Fixed

## Released
0.0.16 02-06-2025

### Added
[SIM-3154]  - Keyclaock changes of route

### Changed

### Removed

### Fixed

## Released
0.0.15 01-05-2025

### Added

### Changed

### Removed

### Fixed
[SIM-3031] - Need to fix toast issue for success message and UI of View Automation in account management


## Released
0.0.14 29-04-2025

### Added

### Changed
[SIM-2824] - node and npm update

### Removed

### Fixed
[SIM-2936] - trivy fixed

## Released
0.0.13 27-03-2025

### Added

### Changed

### Removed

### Fixed
[SIM] - Toggle changes in rule form.

## Released
0.0.12 06-03-2025

### Added

### Changed

### Removed

### Fixed
[SIM] -  Changed Tooltip and some css changes

## Released
0.0.11 05-03-2025

### Added
[SIM-2755] - As a BT/Account Admin, I want to create 'Monthly Pooled Data Usage' rule
[SIM-2759] - As a BT/Account Admin, I want to see 'Monthly Pooled Data Usage' rule in the list

### Changed

### Removed

### Fixed

## Released
0.0.10 17-02-2025

### Added

### Changed

### Removed

### Fixed
[SIM] - Fixed rule form changes for CA.

## Released
0.0.9 14-02-2025

### Added
[SIM-2608] - Automation Rule Form changes Change Rate Plan action added

### Changed

### Removed

### Fixed

## Released
0.0.8 30-01-2025

### Added
[SPOG-2621] - [UI] Lock feature in automation

### Changed

### Removed

### Fixed

## Released
0.0.7 22-01-2025

### Added

### Changed

### Removed

### Fixed
[SIM-2062] - Top bar item showing on wrong place while switching modules

## Released
0.0.6 23-12-2024

### Added

### Changed
[SIM-2648] - Improvements and feedback for Automation Rule
[SIM-2625]- Pagination updated double arrow

### Removed

### Fixed

## Released
0.0.5 27-11-2024

### Added
[SIM-2630] - Confirmation Box added

### Changed

### Removed

### Fixed

## Released
0.0.4 19-11-2024

### Added
[SIM-2207] - As a BT Admin, I want to get to Automation Rules Module

### Changed

### Removed

### Fixed

## Released
0.0.3 26-07-2024

### Added

### Changed

### Removed

### Fixed
[SPOG-2472] Implemented automation rules permission

### Released
0.0.2 22-07-2024

### Added
[SPOG-2414] - Configure GitLab Merge Request Template

### Changed

### Removed

### Fixed

