const sassResourcesLoader = require('craco-sass-resources-loader');
const ModuleFederationPlugin = require('webpack/lib/container/ModuleFederationPlugin');
const { whenProd, whenDev } = require('@craco/craco');
const { dependencies } = require('./package.json');

module.exports = {
  typescript: {
    enableTypeChecking: true,
  },
  plugins: [
    {
      plugin: sassResourcesLoader,
      options: {
        resources: [
          'node_modules/@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss',
          'src/assets/styles/variables.scss',
        ],
      },
    },
  ],
  webpack: {
    configure: (webpackConfig) => {
       
      webpackConfig.plugins = [
        ...webpackConfig.plugins,
        new ModuleFederationPlugin({
          name: 'simAutomation',
          filename: 'remoteEntry.js?V=1.0.1',
          exposes: {
            './SimAutomation': './src/App',
          },
          shared: {
            ...dependencies,
            react: {
              eager: true,
              singleton: true,
              requiredVersion: dependencies.react,
            },
            'react-router-dom': {
              eager: true,
              requiredVersion: dependencies['react-router-dom'],
            },
            'react-dom': {
              singleton: true,
              eager: true,
              requiredVersion: dependencies['react-dom'],
            },
            'react-cookie': {
              singleton: true,
              eager: true,
              requiredVersion: dependencies['react-cookie'],
            },
            axios: {
              singleton: true,
              eager: true,
              requiredVersion: dependencies.axios,
            },
            'react-toastify': {
              singleton: true,
              eager: true,
              requiredVersion: dependencies['react-toastify'],
            },
            formik: {
              singleton: true,
              eager: true,
              requiredVersion: dependencies.formik,
            },
            yup: {
              singleton: true,
              eager: true,
              requiredVersion: dependencies.yup,
            },
            '@mui/icons-material': {
              eager: true,
              singleton: true,
              requiredVersion: dependencies['@mui/icons-material'],
            },
            '@mui/lab': {
              eager: true,
              singleton: true,
              requiredVersion: dependencies['@mui/lab'],
            },
            '@mui/material': {
              eager: true,
              singleton: true,
              requiredVersion: dependencies['@mui/material'],
            },
            '@mui/utils': {
              eager: true,
              singleton: true,
              requiredVersion: dependencies['@mui/utils'],
            },
            '@material-ui/core': {
              eager: true,
              singleton: true,
              requiredVersion: dependencies['@material-ui/core'],
            },
            '@mui/styles': {
              eager: true,
              singleton: true,
              requiredVersion: dependencies['@mui/styles'],
            },
            'react-hook-form': {
              eager: true,
              singleton: true,
              requiredVersion: dependencies['react-hook-form'],
            },
            '@ant-design/icons': { eager: true, requiredVersion: dependencies['@ant-design/icons'] },
            'prop-types': { eager: true, requiredVersion: dependencies['prop-types'] },
            'react-number-format': {
              eager: true,
            },
          },
        }),
      ];

       
      webpackConfig.output = {
        ...webpackConfig.output,
        // for start as container part
        ...whenDev(() => ({ publicPath: 'auto', clean: true })),
        // for start as independent application
        // ...whenDev(() => ({ publicPath: '/', clean: true })),
        ...whenProd(() => ({ publicPath: 'auto', clean: true })),
      };

      return webpackConfig;
    },
  },
};
