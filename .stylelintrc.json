{"extends": ["stylelint-config-standard-scss"], "plugins": ["stylelint-scss"], "rules": {"color-function-notation": null, "alpha-value-notation": null, "length-zero-no-unit": null, "declaration-colon-newline-after": null, "indentation": null, "rule-empty-line-before": null, "at-rule-no-unknown": null, "block-closing-brace-newline-after": ["always", {"ignoreAtRules": ["if", "else"]}], "scss/at-rule-no-unknown": true, "at-rule-name-space-after": "always", "block-opening-brace-space-before": "always", "selector-list-comma-newline-after": null, "selector-class-pattern": null, "keyframes-name-pattern": null}}