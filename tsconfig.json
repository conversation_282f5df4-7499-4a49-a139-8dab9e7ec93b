{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react", "baseUrl": "src", "strictFunctionTypes": true, "exactOptionalPropertyTypes": true, "strictNullChecks": true, "noImplicitAny": false, "noImplicitThis": true}, "exclude": ["src/**.spec.ts"]}