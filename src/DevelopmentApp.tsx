import React, { useEffect, useState } from 'react';
import { BrowserRouter } from 'react-router-dom';
import App from 'App';
import { coreAxios } from 'core/services/HTTPService';
import { IUser } from 'user.model';
import getUser from 'api.service';

const DevelopmentApp = () => {
  const [getUserTrigger, setGetUserTrigger] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<IUser>();

  useEffect(() => {
    if (!coreAxios.defaults.baseURL) {
      // will wait till the base url has been set
      setTimeout(() => {
        setGetUserTrigger((trigger) => trigger + 1);
      }, 100);
      return;
    }

    (async () => {
      try {
        setIsLoading(true);
        const { data } = await getUser();
        setUser(data);
      } finally {
        setIsLoading(false);
      }
    })();
  }, [getUserTrigger]);

  return (
    <BrowserRouter>
      <App
        isLoading={isLoading}
        user={user}
      />
    </BrowserRouter>
  );
};

export default DevelopmentApp;
