import {
  getName,
  getEmail,
  getUserType,
  getUserIdP,
  getActions,
} from './userManagement';

describe('userManagement', () => {
  const fakeUser = {
    name: 'name',
    email: 'email',
    type: 'type',
    identityProvider: {
      displayName: 'displayName',
    },
    actions: 'actions',
  };

  test('getName', () => {
    expect(getName(fakeUser)).toEqual('name');
  });

  test('getEmail', () => {
    expect(getEmail(fakeUser)).toEqual('email');
  });

  test('getUserType', () => {
    expect(getUserType(fakeUser)).toEqual('type');
  });

  test('getUserIdP', () => {
    expect(getUserIdP(fakeUser)).toEqual('displayName');
  });

  test('getActions', () => {
    expect(getActions(fakeUser)).toEqual('actions');
  });
});
