import React, { ReactNode } from 'react';
import { render } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';
import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';
import { ReactElementLike, ReactNodeLike } from 'prop-types';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import { Form, Formik } from 'formik';
import { ToastContainer } from 'react-toastify';
import routes from 'SimAutomationRoute/routes';
import { PageNotFoundWrapper } from 'SimAutomationRoute/SimAutomationRoutes';
import AppContextProvider, { IAppContext } from 'AppContextProvider';
import permissions from 'hooks/permissions';

const themeName = 'bt';
const currentTheme = themeConfig[themeName];

interface ITestProviderProps {
  children: ReactNodeLike[] | ReactElementLike[] | ReactNode;
  route: string | undefined;
  value: IAppContext
}

const initialValues = { };

const TestProvider = ({ children, route, value }: ITestProviderProps) => {
  const routerProps = {
    ...(route && { initialEntries: [route] }),
  };

  return (
    <ThemeProvider theme={theme(currentTheme)}>
      <AppContextProvider value={value}>
        <MemoryRouter {...routerProps}>
          <ToastContainer />
          <Formik
            onSubmit={() => {
              console.log('submit');
            }}
            initialValues={initialValues}
          >
            <Form>
              <Routes>
                <Route {...routes.simAutomateNotFound} element={<PageNotFoundWrapper />} />
                <Route path="*" element={children as ReactNode} />
              </Routes>
            </Form>
          </Formik>
        </MemoryRouter>
      </AppContextProvider>
    </ThemeProvider>
  );
};

const defaultValues = {
  primaryColor: 'blue',
  secondaryColor: 'blue',
  themeName: 'bt',
   
  getBrandColors: (color: string) => ({ 1: color }),
  access: permissions?.result,
  currentTheme: Object
};

 
export default function testRender(ui: ReactNodeLike[] | ReactElementLike[] |
  ReactNode, route?: string, appContextValue: Partial<IAppContext> = defaultValues) {
  return render(<TestProvider route={route} value={{ ...defaultValues, ...appContextValue }}>{ui}</TestProvider>);
}
