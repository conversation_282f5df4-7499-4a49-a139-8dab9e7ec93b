import React, { useEffect, useState } from 'react';
import { render } from '@testing-library/react';
import useTableSize from './useTableSize';

 
const TestComponent = ({ containerClass }) => {
  const [tableContainer, setTableContainer] = useState({});
  const { maxBodyHeight } = useTableSize(tableContainer);
  useEffect(() => {
    setTableContainer(document.querySelector(`.${containerClass}`));
  }, []);

  return (
    <div
      className={containerClass}
    >
      <table className="simple-table" style={{ height: `${maxBodyHeight}px`, margin: '20px' }} />
    </div>
  );
};

describe('Use Table Size: set maxBodyHeight to the table ', () => {
  const setHeight = (height) => (Object.defineProperty(HTMLElement.prototype, 'clientHeight', {
    configurable: true,
    value: height,
  }));

  window.HTMLElement.prototype.getBoundingClientRect = function () {
    return {
      width: parseFloat(this.style.width) || 0,
      height: parseFloat(this.style.height) || 0,
      top: parseFloat(this.style.marginTop) || 0,
      left: parseFloat(this.style.marginLeft) || 0,
    };
  };

  test('should set maxBodyHeight to the table height', () => {
    const testHeightValue = 700;
    const testContainerClass = 'container';
    setHeight(testHeightValue);
    const { getByRole } = render(<TestComponent containerClass={testContainerClass} />);

    const actualResult = getByRole('table').style.height;
    const expectedResult = testHeightValue - getByRole('table').getBoundingClientRect().top;
    expect(actualResult).toEqual(`${expectedResult}px`);
  });

  test('should set maxBodyHeight to the table height equal to 0px', () => {
    const expectedResult = '0px';
    const { getByRole } = render(<TestComponent />);
    const actualResult = getByRole('table').style.height;
    expect(actualResult).toEqual(expectedResult);
  });
});
