import IPermission from 'model/IPermission';

 const parsePermissions = (access, repoName) => {
    const finalValue: Array<IPermission> = [];
    if (access && access.length > 0) {
        const accessValue = access.filter((x) => repoName.includes(x.name));

        if (accessValue) {
            accessValue?.map((x) => x.permission).reduce((result, arr) => {
                arr?.forEach((obj) => {
                    finalValue.push(obj);
                });
                return result;
            }, {});
        }
    }
    return finalValue;
};
export default parsePermissions;
