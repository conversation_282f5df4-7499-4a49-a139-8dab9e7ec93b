import isEmptyObject from './isEmptyObject';

describe('isEmptyObject', () => {
  it('returns true for an empty object', () => {
    expect(isEmptyObject({})).toBe(true);
  });

  it('returns false for an object with properties', () => {
    expect(isEmptyObject({ a: 1, b: 2 })).toBe(false);
  });

  it('returns true for an object that is not an object', () => {
    expect(isEmptyObject(null)).toBe(true);
    expect(isEmptyObject(undefined)).toBe(true);
    expect(isEmptyObject('')).toBe(true);
    expect(isEmptyObject(1)).toBe(true);
  });
});
