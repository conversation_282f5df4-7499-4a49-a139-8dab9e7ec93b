export const getName = (data) => (
  data.name ? data.name : ''
);

export const getEmail = (data) => (
  data.email ? data.email : ''
);

export const getUserType = (data) => (
  data.type ? data.type : ''
);

export const getUserIdP = (data) => (
   
  data.identityProvider ? (data.identityProvider.displayName ? data.identityProvider.displayName : data.identityProvider.alias) : ''
);

export const getActions = (data) => (
  data.actions ? data.actions : ''
);

export const removeUser = async (
  dispatch,
  removeUserAction,
  organizationId,
  userId,
  removeUserModalOpen,
  updateAction,
  redirectUrl,
  navigate,
) => {
  try {
    await dispatch(removeUserAction(organizationId, userId));

    if (updateAction) {
      await dispatch(updateAction(organizationId));
    }

    if (redirectUrl) {
      navigate(redirectUrl);
    }

    removeUserModalOpen(false);
  } catch {
    removeUserModalOpen(false);
  }
};
