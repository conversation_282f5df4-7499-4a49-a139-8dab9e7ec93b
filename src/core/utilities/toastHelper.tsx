import React from 'react';
import { toast } from 'react-toastify';
import { AiOutlineCheckCircle, AiOutlineExclamationCircle, AiOutlineInfoCircle } from 'react-icons/ai';
import { ToastOptions } from 'react-toastify/dist/types';

export const TOAST_CONTAINER_ID = 'SPOG_SIM_AUTOMATION';

export const toastError = (content: string, options?: ToastOptions) => {
  toast.error(content, {
    icon: <AiOutlineExclamationCircle />,
    containerId: TOAST_CONTAINER_ID,
    ...options,
  });
};

export const toastInfo = (content: string, options?: ToastOptions) => {
  toast.info(content, {
    icon: <AiOutlineInfoCircle />,
    containerId: TOAST_CONTAINER_ID,
    ...options,
  });
};

export const toastSuccess = (content: string, options?: ToastOptions) => {
  toast.success(content, { 
    icon: <AiOutlineCheckCircle />,
    containerId: TOAST_CONTAINER_ID, ...options });
};
