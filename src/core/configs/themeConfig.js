import BTLogo from 'assets/images/bt-logo.svg';
import nextgenLogo from 'assets/images/ts-nextgen-header-logo.svg';
import TNSLogo from 'assets/images/tns-logo.svg';
import TNSLogoMob from 'assets/images/ts-nextgen-header-logo-mob.svg';

const themeConfig = {
  nextgen: {
    primaryColor: '#2D2A81',
    secondaryColor: '#F3C73C',
    logo: nextgenLogo,
    logoMob: nextgenLogo,
  },
  bt: {
    primaryColor: '#5514B4',
    secondaryColor: '#F200F5',
    logo: BTLogo,
    logoMob: BTLogo,
    typography: {
      fontFamily: [
        'BTCurve, sans-serif',
      ],
    },
  },
  TNS: {
    primaryColor: '#007AC2',
    secondaryColor: '#95C11F',
    logo: TNSLogo,
    logoMob: TNSLogoMob,
  },
};

export default themeConfig;
