import React from 'react';
import { render, screen } from '@testing-library/react';
import { useAppContext } from 'AppContextProvider';
import CommonAuthwrapper from './CommonAuthwrapper';

// Mock dependencies
jest.mock('AppContextProvider', () => ({
  useAppContext: jest.fn(),
}));

jest.mock('@nv2/nv2-pkg-js-shared-components/lib/Forbidden', () => () => <div>Forbidden</div>);
jest.mock('@nv2/nv2-pkg-js-shared-components/lib/AuthWrapper', () => ({ children }: { children: React.ReactNode }) => <div>{children}</div>);
jest.mock('./utilities/getCurrentThemeColors', () => jest.fn());

const mockUseAppContext = useAppContext as jest.MockedFunction<typeof useAppContext>;

describe('CommonAuthwrapper', () => {
  beforeEach(() => {
    mockUseAppContext.mockReturnValue({ access: [] });
  });

  test('renders children when permissions are allowed', () => {
    render(
      <CommonAuthwrapper permission={[]} repository={[]}>
        <div>Child Component</div>
      </CommonAuthwrapper>,
    );

    expect(screen.getByText('Child Component')).toBeInTheDocument();
  });

  test('renders children when no permissions are required', () => {
    render(
      <CommonAuthwrapper permission={[]} repository={[]}>
        <div>Child Component</div>
      </CommonAuthwrapper>,
    );

    expect(screen.getByText('Child Component')).toBeInTheDocument();
  });

  test('handles isComponent prop properly', () => {
    render(
      <CommonAuthwrapper permission={[]} repository={[]} isComponent>
        <div>Child Component</div>
      </CommonAuthwrapper>,
    );

    expect(screen.getByText('Child Component')).toBeInTheDocument();
  });
});
