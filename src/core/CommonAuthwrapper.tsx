import { useAppContext } from 'AppContextProvider';
import React, { FC, ReactElement } from 'react';
import Forbidden from '@nv2/nv2-pkg-js-shared-components/lib/Forbidden';
import AuthWrapper from '@nv2/nv2-pkg-js-shared-components/lib/AuthWrapper';
import getCurrentThemeColors from './utilities/getCurrentThemeColors';

interface IAuthWrapper {
  children: ReactElement | ReactElement[],
  permission: string[],
  repository: string[],
  isComponent?: boolean,
}
const CommonAuthwrapper: FC<IAuthWrapper> = (
  {
    children, permission = [], repository = [], isComponent,
  }) => {
  const { access } = useAppContext();
  const ForbiddenComponent = () => <Forbidden getCurrentThemeColors={getCurrentThemeColors} />;
  return (
    <AuthWrapper
      repository={repository}
      permission={permission}
      access={access}
      isComponent={isComponent}
      Forbidden={ForbiddenComponent}
    >
      {children}
    </AuthWrapper>
  );
};

CommonAuthwrapper.defaultProps = {
  isComponent: false,
};
export default CommonAuthwrapper;
