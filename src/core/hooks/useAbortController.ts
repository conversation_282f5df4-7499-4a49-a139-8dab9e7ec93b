import { useRef } from 'react';

export const abortErrorName = 'CanceledError';

interface IUseAbortController {
  getController: () => AbortController | null;
  getSignal: () => AbortSignal | undefined;
  cancelPreviousRequest: () => void;
  setNewController: () => { controller: AbortController; signal: AbortSignal };
}

const useAbortController = (): IUseAbortController => {
  const abortControllerRef = useRef<AbortController | null>(null);

  const getController = () => abortControllerRef.current;

  const getSignal = () => abortControllerRef.current?.signal;

  const setNewController = () => {
    const controller = new AbortController();
    abortControllerRef.current = controller;

    return {
      controller,
      signal: controller.signal,
    };
  };

  const cancelPreviousRequest = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  };

  return {
    getController,
    getSignal,
    cancelPreviousRequest,
    setNewController,
  };
};

export default useAbortController;
