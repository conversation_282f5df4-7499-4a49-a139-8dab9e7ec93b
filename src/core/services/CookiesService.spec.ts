import CookiesService from './CookiesService';

describe('CookiesService', () => {
  const fakeCookies = {
    'nv2-auth-access-token': 'token',
    'nv2-auth-user-email': 'email',
    'nv2-auth-user-name': 'name',
  };

  test('should return token from cookies', () => {
    expect(CookiesService.getAccessToken(fakeCookies)).toEqual({ value: 'token' });
  });

  test('should return email from cookies', () => {
    expect(CookiesService.getUserEmail(fakeCookies)).toEqual({ value: 'email' });
  });

  test('should return name from cookies', () => {
    expect(CookiesService.getUserName(fakeCookies)).toEqual({ value: 'name' });
  });
});
