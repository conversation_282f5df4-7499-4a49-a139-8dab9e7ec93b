export const mixed = {
  default: 'Invalid',
  required: 'Required field',
};

export const number = {
  integer: 'Must be an integer',
  type: 'The data type must be a number',
  moreThan: (value, label) => `${label} should not be ${value} or less than ${value}`,
};

export const string = {
  max: (value, label) => `${label} must be at most ${value} characters`,
};

export default {
  mixed,
  number,
  string,
};
