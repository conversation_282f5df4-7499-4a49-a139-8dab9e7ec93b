import { useAppContext } from 'AppContextProvider';
import IPermission from 'model/IPermission';
import React, { FC, ReactElement } from 'react';
import Forbidden from '@nv2/nv2-pkg-js-shared-components/lib/Forbidden';
import getCurrentThemeColors from 'core/utilities/getCurrentThemeColors';
import parsePermissions from 'core/utilities/parsePermissions';

export const useGetAuthorization = (rights:string[], repoName) => {
  const { access } = useAppContext();
  let allow = false;
  const finalValue: Array<IPermission> = parsePermissions(access, repoName) || [];
  if (finalValue && finalValue?.length > 0) {
    const finalValueNames = finalValue.map((t) => t.name);

    allow = rights.every((name) => finalValueNames.includes(name));
  }
  return allow;
};

interface IPrivateRotesProps {
  permission: string[],
  repoName: string[],
  children: ReactElement,
}
const PrivateRotes: FC<IPrivateRotesProps> = (
  { permission, repoName, children }): ReactElement => {
    const isAllow = useGetAuthorization(permission, repoName);
    const isAuthorise = isAllow || !permission;
  if ((permission && permission?.length !== 0) && (repoName && repoName?.length !== 0)) {
    if (!isAuthorise) {
      return <Forbidden getCurrentThemeColors={getCurrentThemeColors} />;
    }
  }

  return children;
};

export default PrivateRotes;
