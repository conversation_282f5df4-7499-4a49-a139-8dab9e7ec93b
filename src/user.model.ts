 
export enum organizationTypes {
   
  DISTRIBUTOR = 'DISTRIBUTOR',
   
  CLIENT = 'CLIENT',
}

export interface IOrganization {
  id: number;
  name: string;
  parent_id: string | null;
  type: organizationTypes;
}

export interface IUser {
  email: string;
  firstName: string;
  id: string;
  lastName: string;
  organization: IOrganization;
}

export interface simAutomation {
  uuid: string;
  accountName: string;
  lock: boolean;
  logoKey: string;
  logoUrl: string;
  ruleCategory: string;
  ruleType: string;
  ruleName: string;
  ruleDefinition: string;
  dataVolume: number | string;
  unit: string ;
  status: boolean;
  action: Array<{ name: string; action: string; actionValue: string }>;
  notification: {
    email: string[];
    sms: string[];
    push: string[];
  };
}

export interface IAccountTableRow {
  row: simAutomation;
  onClickHandler?: (id: number) => void;
  onStatusChange?: ((id: string, status: boolean) => void) | undefined;
  onEditClickHandler?: ((id: string) => void) | undefined;
  onViewClickHandler?: ((id: string) => void) | undefined;
  onLockClickHandler?: ((id: string, status: boolean, ruleName: string) => void) | undefined;
  view?: boolean | undefined;
  setDialog: (value: boolean) => void;
  setSelectedRuleStatus: (value: boolean) => void;
  setUuid:(value: string) => void;
  setRuleName :(value: string) => void;
}

export interface IAccountName {
  id: number;
  name: string;
  organization_id: number;
}

export interface IAccountInfo {
  id: number;
  name: string;
}

export interface UnitOption {
  ref: string;
  value: string;
}

interface Organization {
  name: string;
  id: number;
  parent_id: number | null;
  type: string;
}

export interface User {
  email: string;
  firstName: string;
  lastName: string;
  id: string;
  username: string;
  organization: Organization;
}

export interface IAccountOption {
  id: number;
  value: string;
}

export interface IRatePlan {
  id: number;
  name: string;
  accessFee: number;
  currency: string;
  isDefault: boolean;
  allowanceUsed: number;
  ratePlanModel: number;
  simLimit: number | null;
  originationGroups: any
}

export interface IRatePlanCompany {
  accountId: number;
  accountName: string;
  accountLogoUrl: string;
  ratePlans: IRatePlan[];
}

export type RuleKeys =
| 'status'
| 'ruleType'
| 'ruleCategory'
| 'ruleDefinition'
| 'ruleName'
| 'dataVolume'
| 'volumeUnit'
| 'deActivateSim'
| 'changeRatePlan'
| 'reActivateSIM'
| 'source'
| 'ratePlanAction'
| 'target'
| 'emailStatus'
| 'primaryEmail'
| 'lock';

export interface Option {
  id?: number | string;
  value: string;
  ref?: string;
}

export interface RuleFormProps {
  units: any;
  ruleAction: Option[];
  ratePlansActions: Option[];
  onClose: () => void;
  ruleId: string;
  onSave: () => void;
  setStatusToggle: (value: boolean) => void;
  setStatusCounter: () => void;
  selectedEmail: string[];
  view?: boolean;
  create?: boolean;
  isAdmin?: boolean;
  accountInfo: IAccountInfo;
  personalAccount: any;
  setIsView: (value: boolean) => void;
}

export interface allCategoryProps {
  id: number | string;
  ruleTypeId: number | string;
  category: string;
  ruleType: string;
}
