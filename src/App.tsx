import React, { useEffect, useState } from 'react';
import { useCookies } from 'react-cookie';
import { useNavigate } from 'react-router-dom';
import 'react-toastify/dist/ReactToastify.css';

import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';
import { coreAxios } from 'core/services/HTTPService';
import { ConfigSettingsService, CookiesService, HTTPService } from 'core/services';
import { ThemeProvider } from '@mui/material/styles';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';
import { ToastContainer } from 'react-toastify';
import { CloseOutlined } from '@ant-design/icons';
import { TOAST_CONTAINER_ID } from 'core/utilities/toastHelper';
import AppContextProvider from 'AppContextProvider';
import { IUser } from 'user.model';
import SimAutomationRoutes from 'SimAutomationRoute';
import Loader from 'shared/Loader';
import { getAccesss } from 'features/AutomationRules/api.service';
import { AiOutlineCheckCircle } from 'react-icons/ai';
import './App.scss';

interface IAppProps {
  user: IUser | undefined,
  isLoading: boolean
}

const defaultThemeName = 'bt';

const App = ({ user, isLoading }: IAppProps) => {
  const navigate = useNavigate();
  const [cookies] = useCookies();
  const [state, setState] = useState({
    themeName: defaultThemeName,
    loadAxiosUrl: false,
  });
  const { themeName } = state;
  const currentTheme = themeConfig[themeName];
  const [access, setAccess] = useState([]);

  const isDevelopmentMode = process.env.NODE_ENV === 'development';
  const getProdLogOutUrl = (keycloakLogOutUrl: string) => {
    const keycloakLogoutUrlFull = `${keycloakLogOutUrl}?redirect_uri=${window.location.origin}`;

    return `/oauth2/sign_out?rd=${encodeURIComponent(keycloakLogoutUrlFull)}`;
  };

  const logOut = async (url: string) => {
    window.location.replace(url);
  };

  const getLogoutUrl = (url: string) => (isDevelopmentMode
    ? process.env.REACT_APP_LOGOUT_URL
    : getProdLogOutUrl(url));

  const redirectToLogin = () => {
    const currentPathname = window.location.href;
    const entryPointUrl = `${process.env.REACT_APP_LOGIN_URL}?entryPath=${currentPathname}`;

    navigate(entryPointUrl);
  };

  const authenticateForDevelopmentMode = () => {
    const cookieDoesntHaveAccessToken = !CookiesService.getAccessToken(cookies).value;

    if (cookieDoesntHaveAccessToken) {
      redirectToLogin();
    }

    HTTPService.setAccessToken(coreAxios, cookies);
  };

  const setupInitialData = async () => {
    const { data } = await ConfigSettingsService.getAppVariables();
    sessionStorage.setItem('AppData', JSON.stringify(data));
    const logoutUrl = getLogoutUrl(data?.themeName === 'bt' ? data?.coreUiUrl : data.keycloakLogoutUrl);

    if (isDevelopmentMode) {
      authenticateForDevelopmentMode();
    }

    HTTPService.setDefaultGlobalConfig(coreAxios, data.apiUrl);

    HTTPService.setCorsError(coreAxios, logOut, logoutUrl);

    const userAcess = await getAccesss();
    if (userAcess && userAcess?.data) {
      setAccess(userAcess?.data?.result);
    }

    setState({
      ...state,
      themeName: data.themeName,
      loadAxiosUrl: true,
    });
  };

  useEffect(() => {
    setupInitialData();
     
    document.body.style.fontFamily = themeConfig.bt.typography.fontFamily[0];
  }, []);

  return (
    <ThemeProvider
      theme={theme(currentTheme)}
    >
      <AppContextProvider value={{ themeName, currentTheme, access }}>
        <ToastContainer
          enableMultiContainer
          containerId={TOAST_CONTAINER_ID}
          position="bottom-right"
          autoClose={5000}
          hideProgressBar
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          closeButton={() => <CloseOutlined />}
          icon={<AiOutlineCheckCircle />}
        />
        {
          state.loadAxiosUrl && user ? (
            <SimAutomationRoutes
              user={user}
              isLoading={isLoading}
            />
          )
            : (
              <div className="routes__loader">
                <Loader size={60} />
              </div>
            )
        }
      </AppContextProvider>
    </ThemeProvider>
  );
};

export default App;
