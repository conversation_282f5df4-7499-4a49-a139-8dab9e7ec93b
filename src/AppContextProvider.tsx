import React, {
  createContext, useMemo, ReactElement,
} from 'react';
import IResource from 'model/IResource';
import getCurrentThemeColors from 'core/utilities/getCurrentThemeColors';

export interface IAppContext {
  primaryColor: string,
  secondaryColor: string,
  themeName: string,
  getBrandColors: (color: string) => ({ [index: number]: string }),
  access: Array<IResource>,
  currentTheme: any
}

const AppContext = createContext<Partial<IAppContext>>({});

export interface IAppContextProvider {
  children: ReactElement | ReactElement[] | null;
  value: {
    [key: string] : any
  }
}

const AppContextProvider = ({ children, value }: IAppContextProvider) => {
  const { themeName, currentTheme, access } = value;

  const themeVariables = {
    primaryColor: currentTheme?.primaryColor,
    secondaryColor: currentTheme?.secondaryColor,
    getBrandColors: getCurrentThemeColors,
    themeName,
    access,
    currentTheme
  };

  const appContextValue = useMemo(() => ({
    ...value,
    ...themeVariables,
  }), [themeName, access]);

  return (
    <AppContext.Provider value={appContextValue}>
      {children}
    </AppContext.Provider>
  );
};

const useAppContext = () => React.useContext(AppContext);

export { AppContext, AppContextProvider, useAppContext };

export default AppContextProvider;
