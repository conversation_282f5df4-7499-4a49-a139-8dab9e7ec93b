@import "assets/styles/reset";

body {
  background-color: $app-background-color;
  font-size: 14px;

  .scrollBar::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  .scrollBar::-webkit-scrollbar-thumb {
    // box-shadow: inset 0 0 6px rgba(163, 163, 177, 0.9);
    background-color: #dedee6;
    padding-inline: 2px;
    border-radius: 20px;
  }
}

body::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.black_stroke {
  svg {
    stroke: #000;
  }
}

body::-webkit-scrollbar-thumb {
  // box-shadow: inset 0 0 6px rgba(163, 163, 177, 0.9);
  background-color: #c4c4ce;
  padding-inline: 2px;
  border-radius: 20px;
}

.Toastify {

  &__toast-icon {
    width: 22px;
  }

  &__toast {
    padding-right: 17px;
    font-weight: 700;
    font-size: 14px;
    color: #1c1c28;

    &--error {
      border-left: 3px solid #eb5f64;
      background-color: #fce8e9;

      svg {
        fill: #eb5f64;
      }
    }

    &--info {
      border-left: 3px solid #5514b4;
      background-color: #ebe3f6;
      font-size: 14px;

      svg {
        fill: #5514b4;
      }
    }

    &--success {
      border-left: 3px solid #30b281;
      background-color: #e2f4ed;
      font-weight: 700;
      font-size: 14px;
      color: #1c1c28;

      svg {
        fill: #30b281;
      }
    }

    svg {
      width: 32px;
      height: 32px;
    }
  }

  .anticon-close {
    margin: auto;

    svg {
      width: 20px;
      height: 20px;
      fill: $dark-69;
    }
  }
}

.routes__loader {
  display: flex;
  min-height: 500px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}
