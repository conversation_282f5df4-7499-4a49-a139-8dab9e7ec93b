 
 
import { renderHook } from '@testing-library/react-hooks';
import useAccess from './useAccess';
import permissions from './permissions';

// Mock the 'useAppContext' function
jest.mock('AppContextProvider', () => ({
  useAppContext: () => ({
    access: permissions?.result,
  }),
}));

describe('useAccess', () => {
  it('should return true when the user has the necessary permission', () => {
    const rights = 'Create Account';
    const repoName = 'Accounts';
    const { result } = renderHook(() => useAccess(rights, repoName));
    expect(result.current).toBe(true);
  });

  it('should return false when the user does not have the necessary permission', () => {
    const rights = 'write';
    const repoName = 'AccountManagement';
    const { result } = renderHook(() => useAccess(rights, repoName));
    expect(result.current).toBe(false);
  });

  it('should return false when the repoName is not in the user\'s access list', () => {
    const rights = 'Create Account';
    const repoName = 'MarketShareReport';
    const { result } = renderHook(() => useAccess(rights, repoName));
    expect(result.current).toBe(false);
  });

  it('should return false when the user has no access', () => {
    jest.spyOn(require('AppContextProvider'), 'useAppContext').mockReturnValue({ access: [] });
    const rights = 'Create Account';
    const repoName = 'AccountManagement';
    const { result } = renderHook(() => useAccess(rights, repoName));
    expect(result.current).toBe(false);
  });
});
