import { useAppContext } from 'AppContextProvider';

const checkPermission = (repoName, permission) => {
  const { access } = useAppContext();

    const result = access?.find((repo) => repo.name === repoName);

    if (result) {
        const permissions = result.permission.map((p) => p.name);
        return permissions.includes(permission);
    }

    return false;
};

export default checkPermission;
