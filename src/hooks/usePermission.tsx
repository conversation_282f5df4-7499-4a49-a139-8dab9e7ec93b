import { useAppContext } from 'AppContextProvider';
import IPermission from 'model/IPermission';
import { useEffect, useState } from 'react';

const usePermission = (repoName:Array<string>) => {
  const { access } = useAppContext();
  const [permission, setPermission] = useState <Array<IPermission>>([]);

  useEffect(() => {
    if (access && access.length > 0) {
      const accessValue = access.filter((x) => repoName.includes(x.name));
      if (accessValue) {
        const finalValue: Array<IPermission> = [];
        accessValue?.map((x) => x.permission).reduce((result, arr) => {
          arr?.forEach((obj) => {
            finalValue.push(obj);
          });
          return result;
        }, {});
        setPermission(finalValue);
      }
    }
  }, [JSON.stringify(access)]);

  return permission;
};

export default usePermission;
