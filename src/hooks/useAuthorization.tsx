import { useEffect, useState } from 'react';
import usePermission from './usePermission';

const useAuthorization = (rights:string[], repoName:string[]) => {
  const permission = usePermission(repoName);
  const [isAuthorise, setIsAuthorise] = useState(false);
  useEffect(() => {
    if (rights && permission && permission.length > 0) {
      // Check if 'rights' is in the 'permission' array
      const finalValueNames = permission.map((t) => t.name);

      // Check if 'rights' is in the 'permission' array
      const allow = rights?.every((name) => finalValueNames.includes(name));
      // const allow = permission.some((x) => x.name === rights);

      // Only update 'isAuthorise' if 'allow' changes
      if (isAuthorise !== allow) {
        setIsAuthorise(allow);
      }
    } else {
      // If 'permission' is empty or null, set 'isAuthorise' to false
      setIsAuthorise(false);
    }
  }, [permission, rights]); // Use 'permission' and 'rights' as dependencies

  return isAuthorise;
};

export default useAuthorization;
