import { useAppContext } from 'AppContextProvider';
import parsePermissions from 'core/utilities/parsePermissions';
import IPermission from 'model/IPermission';
import { useEffect, useState } from 'react';

const useAccess = (rights, repoName) => {
  const { access } = useAppContext();
  const [allow, setAllow] = useState(false);

  useEffect(() => {
    const finalValue: Array<IPermission> = parsePermissions(access, repoName) || [];
    if (finalValue && finalValue?.length > 0) {
      // Check if 'rights' is in the 'permission' array
      setAllow(finalValue.some((x) => x.name === rights));
    } else {
      setAllow(false);
    }
    // Only update 'isAuthorise' if 'allow' changes
  }, []);

  return allow;
};

export default useAccess;
