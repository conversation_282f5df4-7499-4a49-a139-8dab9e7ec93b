import { renderHook } from '@testing-library/react';
import { useApplyCSSVariables } from './useApplyCSSVariables';

describe('useApplyCSSVariables', () => {
  let mockSetProperty: jest.Mock;

  beforeEach(() => {
    mockSetProperty = jest.fn();
    Object.defineProperty(document.documentElement, 'style', {
      value: {
        setProperty: mockSetProperty,
      },
      writable: true,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should not set CSS variables when theme is null', () => {
    renderHook(() => useApplyCSSVariables(null));

    expect(mockSetProperty).not.toHaveBeenCalled();
  });

  it('should not set CSS variables when theme is undefined', () => {
    renderHook(() => useApplyCSSVariables(undefined));

    expect(mockSetProperty).not.toHaveBeenCalled();
  });

  it('should set CSS variables when theme is provided with all properties', () => {
    const theme = {
      primaryColor: '#5514B4',
      actionBg: '#ebe3f6',
      btTextColor: '#ffffff',
      bgLightPrimary: '#f5f1fa',
      bgLightIntermediatePrimary: '#ccb9e9',
    };

    renderHook(() => useApplyCSSVariables(theme));

    expect(mockSetProperty).toHaveBeenCalledWith('--primary-color', '#5514B4');
    expect(mockSetProperty).toHaveBeenCalledWith('--action-bg', '#ebe3f6');
    expect(mockSetProperty).toHaveBeenCalledWith('--primary-lightColor', '#ffffff');
    expect(mockSetProperty).toHaveBeenCalledWith('--light-background', '#f5f1fa');
    expect(mockSetProperty).toHaveBeenCalledWith('--intermidate-bg', '#ccb9e9');
    expect(mockSetProperty).toHaveBeenCalledTimes(5);
  });

  it('should set CSS variables with default value for bgLightPrimary when not provided', () => {
    const theme = {
      primaryColor: '#5514B4',
      actionBg: '#ebe3f6',
      btTextColor: '#ffffff',
      bgLightIntermediatePrimary: '#ccb9e9',
    };

    renderHook(() => useApplyCSSVariables(theme));

    expect(mockSetProperty).toHaveBeenCalledWith('--light-background', '#f5f1fa');
  });

  it('should only set CSS variables for properties that have values', () => {
    const theme = {
      primaryColor: '#5514B4',
      actionBg: undefined,
      btTextColor: null,
      bgLightPrimary: '',
      bgLightIntermediatePrimary: '#ccb9e9',
    };

    renderHook(() => useApplyCSSVariables(theme));

    expect(mockSetProperty).toHaveBeenCalledWith('--primary-color', '#5514B4');
    expect(mockSetProperty).toHaveBeenCalledWith('--intermidate-bg', '#ccb9e9');
    expect(mockSetProperty).toHaveBeenCalledWith('--light-background', '#f5f1fa');
    expect(mockSetProperty).toHaveBeenCalledTimes(3);
  });

  it('should update CSS variables when theme changes', () => {
    const initialTheme = {
      primaryColor: '#5514B4',
      actionBg: '#ebe3f6',
    };

    const { rerender } = renderHook(
      ({ theme }) => useApplyCSSVariables(theme),
      { initialProps: { theme: initialTheme } }
    );

    expect(mockSetProperty).toHaveBeenCalledWith('--primary-color', '#5514B4');
    expect(mockSetProperty).toHaveBeenCalledWith('--action-bg', '#ebe3f6');

    mockSetProperty.mockClear();

    const updatedTheme = {
      primaryColor: '#FF0000',
      actionBg: '#00FF00',
    };

    rerender({ theme: updatedTheme });

    expect(mockSetProperty).toHaveBeenCalledWith('--primary-color', '#FF0000');
    expect(mockSetProperty).toHaveBeenCalledWith('--action-bg', '#00FF00');
  });

  it('should handle empty theme object', () => {
    const theme = {};

    renderHook(() => useApplyCSSVariables(theme));

    expect(mockSetProperty).toHaveBeenCalledWith('--light-background', '#f5f1fa');
    expect(mockSetProperty).toHaveBeenCalledTimes(1);
  });
});
