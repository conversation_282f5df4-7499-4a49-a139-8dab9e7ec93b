 
 
import { renderHook } from '@testing-library/react-hooks';
// import usePermission from './usePermission'; // Update the import path accordingly
import { waitFor } from '@testing-library/react';
import permissions from './permissions';
import useAuthorization from './useAuthorization';

// Mock the AppContextProvider module
jest.mock('AppContextProvider', () => ({
  useAppContext: () => ({
    access: permissions?.result,
  }),
}));

describe('useAuthorization', () => {
  it('should return false when permission is empty', () => {
    const repoName = [];
    const authorization = renderHook(() => useAuthorization(['Create Account'], repoName));
    expect(authorization.result.current).toBe(false);
  });

  it('should return false when rights are not in permission', () => {
    const repoName = ['AccountManagement'];
    const authorization = renderHook(() => useAuthorization([''], repoName));
    expect(authorization.result.current).toBe(false);
  });
  it('should return true when rights are in permission', () => {
    const repoName = ['Accounts'];
    const authorization = renderHook(() => useAuthorization(['Create Account'], repoName));
    expect(authorization.result.current).toBe(true);
  });
  it('should update isAuthorise when permission changes', () => {
    const { result, rerender } = renderHook(
      ({ rights, repoName }) => useAuthorization(rights, repoName),
      {
        initialProps: { rights: ['Create Account'], repoName: ['AccountManagement'] },
      },
    );

    // Initially, isAuthorise should be true
    rerender({ rights: ['Create Account'], repoName: ['AccountManagement'] });
    waitFor(() => {
      expect(result.current).toBe(false);
    });
    // Rerender with empty permission - isAuthorise should update to false
    rerender({ rights: ['View Account'], repoName: ['AccountManagement'] });
    waitFor(() => {
      expect(result.current).toBe(false);
    });
  });
});
