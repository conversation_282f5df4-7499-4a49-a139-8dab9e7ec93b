import { useEffect } from 'react';

export const useApplyCSSVariables = (theme) => {
  useEffect(() => {
    if (!theme) return;

    const variables: Record<string, string | undefined> = {
      '--primary-color': theme.primaryColor,
      '--action-bg': theme.actionBg,
      '--primary-lightColor': theme.btTextColor,
      '--light-background': theme.bgLightPrimary || '#f5f1fa',
      '--intermidate-bg': theme.bgLightIntermediatePrimary,
    };

    Object.entries(variables).forEach(([key, value]) => {
      if (value) {
        document.documentElement.style.setProperty(key, value);
      }
    });
  }, [theme]);
};
