 
 
import { renderHook } from '@testing-library/react-hooks';
import { waitFor } from '@testing-library/react';
import usePermission from './usePermission'; // Update the import path accordingly
import permissions from './permissions';

// Mock the AppContextProvider module
jest.mock('AppContextProvider', () => ({
  useAppContext: () => ({
    access: permissions?.result,
  }),
}));

describe('usePermission', () => {
  it('should return an empty array when repoName is not found in access', () => {
    const { result } = renderHook(() => usePermission(['nonExistentRepo']));
    expect(result.current).toEqual([]);
  });

  it('should return the correct permissions for a single repo', () => {
    const { result } = renderHook(() => usePermission(['Accounts']));
    expect(result.current).toEqual([
      {
        id: '44b4abe6-e938-4728-a398-96d6170fa754',
        name: 'View Accounts',
        title: 'View Accounts',
      },
      {
        id: '417853b0-276f-45ab-8eb7-bf774db82616',
        name: 'Modify Account',
        title: 'Modify Account',
      },
      {
        id: '262c6a11-e634-4941-8393-f26d79846d07',
        name: 'View Account Details',
        title: 'View Account Details',
      },
      {
        id: '********-ae38-4c0b-b5e3-94b9c0394277',
        name: 'Delete Account',
        title: 'Delete Account',
      },
      {
        id: '4fd35554-82d9-4b67-b506-2d221aea1dbe',
        name: 'Create Account',
        title: 'Create Account',
      },
    ]);
  });

  it('should return the correct permissions for multiple repos', () => {
    const { result } = renderHook(() => usePermission(['Audit', 'Accounts']));
    expect(result.current).toEqual([
      {
        id: 'c73f486f-4968-4c46-8210-1a78a26387d5',
        name: 'SIM Audit Logs',
        title: 'SIM Audit Logs',
      },
      {
        id: '40c13387-3885-4a1b-9ea3-60b8490d64d7',
        name: 'Account Audit Logs',
        title: 'Account Audit Logs',
      },
      {
        id: '44b4abe6-e938-4728-a398-96d6170fa754',
        name: 'View Accounts',
        title: 'View Accounts',
      },
      {
        id: '417853b0-276f-45ab-8eb7-bf774db82616',
        name: 'Modify Account',
        title: 'Modify Account',
      },
      {
        id: '262c6a11-e634-4941-8393-f26d79846d07',
        name: 'View Account Details',
        title: 'View Account Details',
      },
      {
        id: '********-ae38-4c0b-b5e3-94b9c0394277',
        name: 'Delete Account',
        title: 'Delete Account',
      },
      {
        id: '4fd35554-82d9-4b67-b506-2d221aea1dbe',
        name: 'Create Account',
        title: 'Create Account',
      },
    ]);
  });

  it('should update permissions when access prop changes', () => {
    const { result, rerender } = renderHook(({ repoName }) => usePermission(repoName), {
      initialProps: { repoName: ['AccountManagement'] },
    });
    waitFor(() => {
      expect(result.current).toEqual([
        {
          id: '0a2beb21-5599-481b-8c78-39be6a148e25',
          name: 'Create Account',
          title: 'Create Account',
        },
        {
          id: 'e6781bb9-9d3d-4fe2-a9de-e233a8c4726a',
          name: 'Modify Account',
          title: 'Modify Account',
        },
        {
          id: '1d21083a-f677-4e38-9ff9-78832cc0b159',
          name: 'View Accounts',
          title: 'list_accounts',
        },
        {
          id: '66a89bd8-9186-4ae4-90a6-8f2cb8504c52',
          name: 'Delete Account',
          title: 'Delete Account',
        },
        {
          id: '2bfe230b-59c2-4307-8ad4-e54768aea424',
          name: 'View Account Details',
          title: 'view_account',
        },
      ]);
    });
    rerender({ repoName: ['AuditLog'] });
    waitFor(() => {
      expect(result.current).toEqual([
        {
          id: '232a965c-fcff-4fa2-811a-80685da27de7',
          name: 'Account Audit Logs',
          title: 'system_audit_logs',
        },
      ]);
    });
  });

  it('should return an empty array when access is empty', () => {
    // Mock the access to be an empty array
    jest.spyOn(require('AppContextProvider'), 'useAppContext').mockReturnValue({ access: [] });

    const { result } = renderHook(() => usePermission(['AccountManagement']));
    expect(result.current).toEqual([]);
  });
});
