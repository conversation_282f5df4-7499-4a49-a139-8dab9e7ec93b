import React from 'react';
import { render, screen } from '@testing-library/react';
import CloseIcon from './CloseIcon'; // Adjust the path if needed

describe('CloseIcon Component', () => {
  it('should contain a <path> element with the correct attributes', () => {
    render(<CloseIcon />);
    const pathElement = screen.getByTestId('close-icon-path');
    expect(pathElement).toHaveAttribute('fill-rule', 'evenodd');
    expect(pathElement).toHaveAttribute('clip-rule', 'evenodd');
    expect(pathElement).toHaveAttribute('fill', '#707070');
  });
});
