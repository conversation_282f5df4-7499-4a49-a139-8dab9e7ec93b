import routes from './routes';

describe('Routes Configuration', () => {
  test('should have simAutomate route', () => {
    expect(routes).toHaveProperty('simAutomate');
    expect(routes.simAutomate).toHaveProperty('path', '/');
  });

  test('should have simAutomateNotFound route', () => {
    expect(routes).toHaveProperty('simAutomateNotFound');
    expect(routes.simAutomateNotFound).toHaveProperty('path', '/not-found');
  });

  test('should not have extra properties', () => {
    const routeKeys = Object.keys(routes);
    expect(routeKeys).toEqual(['simAutomate', 'simAutomateNotFound']);
  });
});
