import React, { createContext, useMemo } from 'react';
import { Routes, Route } from 'react-router-dom';
import PageNotFound from '@nv2/nv2-pkg-js-shared-components/lib/PageNotFound';

import { Box } from '@mui/material';
import LandingPageSkeleton from 'features/AutomationRules/LandingPageSkeleton';
import LandingPage from 'features/AutomationRules/LandingPage';
import { useAppContext } from 'AppContextProvider';
import PrivateRotes from 'PrivateRotes';
import routes from 'SimAutomationRoute/routes';
import { PERMISSION, REPOSITORY } from 'features/constants';
import { IUser, organizationTypes } from 'user.model';
import '../App.scss';
import { useApplyCSSVariables } from 'hooks/useApplyCSSVariables';

const SimAutomationComponent = {
  [organizationTypes.DISTRIBUTOR]: () => <LandingPage isAdmin />,
  [organizationTypes.CLIENT]: () => <LandingPage />,
};

export interface IRouteContext {
  user: IUser | undefined,
  isUserRoleClient: boolean,
}

interface ISimAutomationRoutesProps {
  user: IUser | undefined,
  isLoading: boolean
}

export const PageNotFoundWrapper = () => {
  const { primaryColor, getBrandColors } = useAppContext();

  return (
    <PageNotFound
      primaryColor={primaryColor}
      getCurrentThemeColors={getBrandColors}
    />
  );
};
export const RouteContext = createContext<Partial<IRouteContext>>({});

const SimAutomationRoutes = ({ user }: ISimAutomationRoutesProps) => {
  // const isUserRoleDistributor = user?.organization.type === organizationTypes.DISTRIBUTOR;
  const isUserRoleClient = user?.organization.type === organizationTypes.CLIENT;
  const SimAutomation = user
    ? SimAutomationComponent[user?.organization.type]
    : LandingPageSkeleton;
  // : SimAutomationComponent.CLIENT;

  const { currentTheme } = useAppContext();

  const appContextValue = useMemo(() => ({
    ...user,
    isUserRoleClient,
  }), [isUserRoleClient]);

  useApplyCSSVariables(currentTheme)

  return (
    <Box sx={{ padding: '5px 10px', minHeight: '100vh' }} data-testid="SimAutomationRoutes">
      <RouteContext.Provider value={appContextValue}>
        <Routes>
          <Route
            {...routes.simAutomate}
            element={(
              <PrivateRotes
                permission={[PERMISSION?.VIEW_RULE_DETAIL]}
                repoName={[REPOSITORY?.SIM_AUTOMATION]}
              >
                <SimAutomation />
              </PrivateRotes>
            )}
          />
          <Route {...routes.simAutomateNotFound} element={<PageNotFoundWrapper />} />
          <Route path="*" element={<PageNotFoundWrapper />} />
        </Routes>
      </RouteContext.Provider>
    </Box>
  );
};

export default SimAutomationRoutes;
