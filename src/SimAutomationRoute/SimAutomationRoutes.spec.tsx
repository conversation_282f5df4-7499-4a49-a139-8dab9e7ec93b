import React from 'react';
import { render, screen } from '@testing-library/react';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import { IUser, organizationTypes } from 'user.model';
import SimAutomationRoutes, { PageNotFoundWrapper } from './SimAutomationRoutes';

// Mock components and contexts
jest.mock('AppContextProvider', () => ({
  useAppContext: () => ({
    primaryColor: 'blue',
    getBrandColors: jest.fn(),
  }),
}));

jest.mock('features/AutomationRules/LandingPage', () => () => <div>LandingPage</div>);
jest.mock('features/AutomationRules/LandingPageSkeleton', () => () => <div>LandingPageSkeleton</div>);
jest.mock('@nv2/nv2-pkg-js-shared-components/lib/PageNotFound', () => () => <div>PageNotFound</div>);
jest.mock('PrivateRotes', () => ({ children }: { children: React.ReactNode }) => <div>{children}</div>);

const distributorUser: IUser = {
    id: '1',
    organization: {
        type: organizationTypes.DISTRIBUTOR,
        id: 0,
        name: '',
        parent_id: null,
    },
    email: '',
    firstName: '',
    lastName: '',
};

const clientUser: IUser = {
    id: '2',
    organization: {
        type: organizationTypes.CLIENT,
        id: 0,
        name: '',
        parent_id: null,
    },
    email: '',
    firstName: '',
    lastName: '',
};

describe('SimAutomationRoutes', () => {
  test('renders LandingPage for DISTRIBUTOR user', () => {
    render(
      <MemoryRouter initialEntries={['/']}>
        <SimAutomationRoutes user={distributorUser} isLoading={false} />
      </MemoryRouter>,
    );
    expect(screen.getByText('LandingPage')).toBeInTheDocument();
  });

  test('renders LandingPage for CLIENT user', () => {
    render(
      <MemoryRouter initialEntries={['/']}>
        <SimAutomationRoutes user={clientUser} isLoading={false} />
      </MemoryRouter>,
    );
    expect(screen.getByText('LandingPage')).toBeInTheDocument();
  });

  test('renders LandingPageSkeleton when user is undefined', () => {
    render(
      <MemoryRouter initialEntries={['/']}>
        <SimAutomationRoutes user={undefined} isLoading={false} />
      </MemoryRouter>,
    );
    expect(screen.getByText('LandingPageSkeleton')).toBeInTheDocument();
  });

  test('renders PageNotFoundWrapper for an undefined route', () => {
    render(
      <MemoryRouter initialEntries={['/undefined-route']}>
        <Routes>
          <Route path="*" element={<PageNotFoundWrapper />} />
        </Routes>
      </MemoryRouter>,
    );
    expect(screen.getByText('PageNotFound')).toBeInTheDocument();
  });
});
