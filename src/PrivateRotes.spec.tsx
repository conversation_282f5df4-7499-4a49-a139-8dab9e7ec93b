import React from 'react';
import { waitFor, screen } from '@testing-library/react';
import PrivateRotes from 'PrivateRotes';
import testRender from 'core/utilities/testUtils';

// Mock the LogoutComponent
jest.mock('src/shared/LogoutComponent', () => {
  return function MockLogoutComponent() {
    return <div data-testid="logout-component">LogoutComponent</div>;
  };
});

// Mock AppContextProvider
jest.mock('AppContextProvider', () => ({
  useAppContext: () => ({
    access: {
      SIMManagement: ['someValidItem', '/'],
      SIM_AUTOMATION: ['CREATE_RULE', 'EDIT_RULE', 'VIEW_RULES'],
    },
  }),
}));

const TestChild = () => <span data-testid="test-child">Hello World</span>;

describe('PrivateRotes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component when authorization is granted', () => {
    const mockItem = 'someValidItem';
    const repository = ['SIMManagement'];

    testRender(
      <PrivateRotes
        permission={[mockItem]}
        repoName={repository}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByText('Hello World')).toBeInTheDocument();
  });

  it('renders LogoutComponent when authorization is not granted', async () => {
    const mockItem = 'someInvalidItem';
    const repository = ['SIMManagement'];

    testRender(
      <PrivateRotes
        permission={[mockItem]}
        repoName={repository}
      >
        <TestChild />
      </PrivateRotes>,
    );

    await waitFor(() => {
      expect(screen.getByTestId('logout-component')).toBeInTheDocument();
    });
  });

  it('renders the component with default permission "/"', () => {
    const repository = ['SIMManagement'];

    testRender(
      <PrivateRotes
        permission={['/']}
        repoName={repository}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('test-child')).toBeInTheDocument();
  });

  it('handles multiple permissions correctly', () => {
    const permissions = ['someValidItem', 'anotherValidItem'];
    const repository = ['SIMManagement'];

    testRender(
      <PrivateRotes
        permission={permissions}
        repoName={repository}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('test-child')).toBeInTheDocument();
  });

  it('handles multiple repositories correctly', () => {
    const permissions = ['CREATE_RULE'];
    const repositories = ['SIM_AUTOMATION', 'SIMManagement'];

    testRender(
      <PrivateRotes
        permission={permissions}
        repoName={repositories}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('test-child')).toBeInTheDocument();
  });

  it('denies access when permission exists but not in the specified repository', () => {
    const permissions = ['nonExistentPermission'];
    const repository = ['SIMManagement'];

    testRender(
      <PrivateRotes
        permission={permissions}
        repoName={repository}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('logout-component')).toBeInTheDocument();
  });

  it('handles empty permission array', () => {
    const repository = ['SIMManagement'];

    testRender(
      <PrivateRotes
        permission={[]}
        repoName={repository}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('logout-component')).toBeInTheDocument();
  });

  it('handles empty repository array', () => {
    const permissions = ['someValidItem'];

    testRender(
      <PrivateRotes
        permission={permissions}
        repoName={[]}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('logout-component')).toBeInTheDocument();
  });

  it('handles undefined access context', () => {
    // Mock AppContextProvider to return undefined access
    jest.doMock('AppContextProvider', () => ({
      useAppContext: () => ({
        access: undefined,
      }),
    }));

    const permissions = ['someValidItem'];
    const repository = ['SIMManagement'];

    testRender(
      <PrivateRotes
        permission={permissions}
        repoName={repository}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('logout-component')).toBeInTheDocument();
  });

  it('handles null access context', () => {
    // Mock AppContextProvider to return null access
    jest.doMock('AppContextProvider', () => ({
      useAppContext: () => ({
        access: null,
      }),
    }));

    const permissions = ['someValidItem'];
    const repository = ['SIMManagement'];

    testRender(
      <PrivateRotes
        permission={permissions}
        repoName={repository}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('logout-component')).toBeInTheDocument();
  });
});
