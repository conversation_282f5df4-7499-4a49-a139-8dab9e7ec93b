import React from 'react';
import { render } from '@testing-library/react';
import PrivateRotes from './PrivateRotes';

// Mock the Forbidden component
jest.mock('@nv2/nv2-pkg-js-shared-components/lib/Forbidden', () => {
  return function MockForbidden() {
    return <div data-testid="forbidden-component">Access Forbidden</div>;
  };
});

// Mock AppContextProvider
jest.mock('AppContextProvider', () => ({
  useAppContext: () => ({
    access: []
  }),
}));

describe('PrivateRotes', () => {
  test('component can be imported and instantiated', () => {
    expect(PrivateRotes).toBeDefined();
    expect(typeof PrivateRotes).toBe('function');
  });

  test('renders children when no permission check needed', () => {
    const TestChild = () => <div data-testid="test-child">Test Content</div>;

    const { getByTestId } = render(
      <PrivateRotes permission={[]} repoName={[]}>
        <TestChild />
      </PrivateRotes>
    );

    expect(getByTestId('test-child')).toBeInTheDocument();
  });
});
    const mockItem = 'someInvalidItem';
    const repository = ['SIMManagement'];

    testRender(
      <PrivateRotes
        permission={[mockItem]}
        repoName={repository}
      >
        <TestChild />
      </PrivateRotes>,
    );

    await waitFor(() => {
      expect(screen.getByTestId('logout-component')).toBeInTheDocument();
    });
  });

  it('renders the component with default permission "/"', () => {
    const repository = ['SIMManagement'];

    testRender(
      <PrivateRotes
        permission={['/']}
        repoName={repository}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('test-child')).toBeInTheDocument();
  });

  it('handles multiple permissions correctly', () => {
    const permissions = ['someValidItem', 'anotherValidItem'];
    const repository = ['SIMManagement'];

    testRender(
      <PrivateRotes
        permission={permissions}
        repoName={repository}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('test-child')).toBeInTheDocument();
  });

  it('handles multiple repositories correctly', () => {
    const permissions = ['CREATE_RULE'];
    const repositories = ['SIM_AUTOMATION', 'SIMManagement'];

    testRender(
      <PrivateRotes
        permission={permissions}
        repoName={repositories}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('test-child')).toBeInTheDocument();
  });

  it('denies access when permission exists but not in the specified repository', () => {
    const permissions = ['nonExistentPermission'];
    const repository = ['SIMManagement'];

    testRender(
      <PrivateRotes
        permission={permissions}
        repoName={repository}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('logout-component')).toBeInTheDocument();
  });

  it('handles empty permission array', () => {
    const repository = ['SIMManagement'];

    testRender(
      <PrivateRotes
        permission={[]}
        repoName={repository}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('logout-component')).toBeInTheDocument();
  });

  it('handles empty repository array', () => {
    const permissions = ['someValidItem'];

    testRender(
      <PrivateRotes
        permission={permissions}
        repoName={[]}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('logout-component')).toBeInTheDocument();
  });

  it('handles undefined access context', () => {
    // Mock AppContextProvider to return undefined access
    jest.doMock('AppContextProvider', () => ({
      useAppContext: () => ({
        access: undefined,
      }),
    }));

    const permissions = ['someValidItem'];
    const repository = ['SIMManagement'];

    testRender(
      <PrivateRotes
        permission={permissions}
        repoName={repository}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('logout-component')).toBeInTheDocument();
  });

  it('handles null access context', () => {
    // Mock AppContextProvider to return null access
    jest.doMock('AppContextProvider', () => ({
      useAppContext: () => ({
        access: null,
      }),
    }));

    const permissions = ['someValidItem'];
    const repository = ['SIMManagement'];

    testRender(
      <PrivateRotes
        permission={permissions}
        repoName={repository}
      >
        <TestChild />
      </PrivateRotes>,
    );

    expect(screen.getByTestId('logout-component')).toBeInTheDocument();
  });
});
