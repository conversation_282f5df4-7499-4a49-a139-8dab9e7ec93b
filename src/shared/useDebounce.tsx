import { useState } from 'react';

 const useDebounce = <T extends any[]>(callback: (...args: T) => void, delay: number) => {
  const [timer, setTimer] = useState<number | undefined>(undefined);

  return (...args: T) => {
    if (timer) {
      clearTimeout(timer);
    }

    setTimer(
      window.setTimeout(() => {
        callback(...args);
      }, delay),
    );
  };
};

export default useDebounce;
