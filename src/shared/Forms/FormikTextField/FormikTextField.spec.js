import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { Formik } from 'formik';
import { FormikTextField } from './FormikTextField';

describe('FormikTextField', () => {
  it('renders correctly', () => {
    const { getByTestId } = render(
      <Formik initialValues={{ testField: '' }} onSubmit={() => {}}>
        <FormikTextField name="testField" label="Test Field" />
      </Formik>,
    );
    expect(getByTestId('text_field')).toBeInTheDocument();
  });

  it('handles onChange event correctly', () => {
    const { getByTestId } = render(
      <Formik initialValues={{ testField: '' }} onSubmit={() => {}}>
        <FormikTextField name="testField" label="Test Field" type="number" />
      </Formik>,
    );
    const input = getByTestId('text-field');
    fireEvent.change(input, { target: { value: '222' } });
    expect(input.value).toBe('222');
  });
});
