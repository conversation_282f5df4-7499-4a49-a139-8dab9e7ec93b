.text-field, .numeric-field {
  .MuiInputBase-root {
      /* stylelint-disable */
      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      input[type=number] {
        -moz-appearance: textfield;
    }
      /* stylelint-enable */
  }

  .MuiFormLabel-root {
    top: -3px;
    left: -3px;
    color: $dark-69 !important;
  }

  .MuiFormHelperText-root {
    margin-top: 0;
    position: absolute;
    top: 40px;
  }

  input {
    width: 100%;
  }

  @media screen and (max-width: 1600px) {
    .MuiInputBase-root, .MuiFormLabel-root {
      font-size: 13px !important;
    }
  }
}
