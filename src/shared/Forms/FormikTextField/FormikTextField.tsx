import React, { memo, useEffect, useState } from 'react';
import { TextFieldProps, TextField } from '@mui/material';
import { useField } from 'formik';
import usePropagateRef from 'shared/Forms/usePropagateRef';
import './FormikTextField.scss';

export type FormikTextFieldProps = Omit<TextFieldProps, 'name'> & {
  name: string;
  disablePerformance?: boolean;
  loading?: boolean;
  className?: string,
  type?: string,
};

export const FormikTextField: React.FC<FormikTextFieldProps> = memo(
  (props: FormikTextFieldProps) => {
    const [field, meta] = useField(props.name);
    const error = !!meta.error && meta.touched;
    const [fieldValue, setFieldValue] = useState<string | number>(field.value);
    const {
      className, disablePerformance, loading, ...otherProps
    } = props;

    usePropagateRef({
      setFieldValue,
      name: props.name,
      value: field.value,
    });

    useEffect(() => {
      if (meta.touched) {
        return;
      }
      if (field.value !== fieldValue) {
        setFieldValue(field.value);
      }
    }, [field.value]);

    const onChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
      setFieldValue(evt.target.value);
    };
    const onBlur = (evt: React.FocusEvent<HTMLInputElement>) => {
      const val = evt.target.value || '';
      window.setTimeout(() => {
        field.onChange({
          target: {
            name: props.name,
            value: props.type === 'number' ? +val : val,
          },
        });
      }, 0);
    };

    const performanceProps = disablePerformance
      ? {
        ...field,
        value: loading ? 'Loading...' : fieldValue,
      }
      : {
        ...field,
        value: loading ? 'Loading...' : fieldValue,
        onChange,
        onBlur,
        onFocus: onBlur,
      };
    return (
      <TextField
        {...otherProps}
        className={`text-field ${className}`}
        data-testid="text_field"
        autoComplete="off"
        InputProps={{
          ...((props.type === 'number' && {
            inputProps: { 'data-testid': 'text-field', step: 'any' },
          })
          || undefined),
        }}
        error={error}
        {...performanceProps}
      />
    );
  },
);

FormikTextField.defaultProps = {
  className: '',
  disablePerformance: false,
  loading: false,
  type: 'string',
};
