import { Box } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import * as React from 'react';
import {
  Controller,
} from 'react-hook-form';
import { GrFormDown } from 'react-icons/gr';

interface Option {
  value: string | number;
}

interface RHFAutocompleteFieldProps {
  control: any;
  name: string;
  options: Option[];
  placeholder?: string;
  autoCompleteClasss?: string;
  popperClass?: string;
  popupIcon?: React.ReactNode;
  id?: string;
  rules?: any;
  disableClearable?: boolean;
  disabled?: boolean;
  defaultValue?: any;
  clearErrors?: any;
  fullWidth?: boolean;
  noOptionsText?: string;
  renderOption?: (prop: any, option: any) => React.ReactNode;
  optionLabel?: (option: any) => string;
  // open?: boolean;
  truncateText?: boolean; // ✅ Optional truncation
  maxLength?: number;
}

const truncate = (text: string, maxLength: number) => (text.length > maxLength ? `${text.slice(0, maxLength)}...` : text);

const RHFAutocompleteField = (
  props: RHFAutocompleteFieldProps,
) => {
  const {
    control, options, name,
    popupIcon, id, disableClearable = false,
    autoCompleteClasss, rules, disabled = false, popperClass = 'customAutoComplete',
    defaultValue, clearErrors, fullWidth = false,
    noOptionsText, renderOption,
    truncateText = false, // open = false Default is false (optional)
    maxLength = 25, optionLabel,
  } = props;
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field, fieldState: { error } }) => {
        const { onChange, value, ref } = field;
        return (
          <Autocomplete
            sx={{
              marginTop: '24px',
            }}
            slotProps={{
              popper: {
                className: popperClass,
              },
            }}
            value={
              value
                ? options?.find((option) => value.value === option?.value) ?? ''
                : null
            }
            fullWidth={fullWidth}
            disabled={disabled}
            defaultValue={defaultValue?.value || value}
            disableClearable={disableClearable}
             
            getOptionLabel={optionLabel ? optionLabel : (option) => (truncateText
              ? truncate(option?.value?.toString(), maxLength) : option?.value?.toString())}
            onChange={async (event: any, newValue) => {
              await onChange(newValue || null);
              if (clearErrors) {
                clearErrors(name);
              }
            }}
            className={`customAutoComplete ${autoCompleteClasss}`}
            id={`autocomplete ${id}`}
            options={options}
            noOptionsText={noOptionsText}
            // clearIcon={<CloseOutlined size={18} onClick={() => clearErrors(name)} />}
            popupIcon={popupIcon || <GrFormDown size={18} />}
            renderOption={renderOption || ((prop, option) => (
              <li {...prop} key={option.value} style={{ listStyle: 'none' }}>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '100%',
                    padding: '8px',
                    cursor: 'pointer',
                    '&:hover': {
                      backgroundColor: 'transparent',
                    },
                    '&:active': {
                      backgroundColor: 'transparent',
                    },
                    '&.Mui-focused': {
                      backgroundColor: 'transparent', // ✅ Fix focus issue
                    },
                  }}
                >
                  {truncateText ? truncate(option?.value?.toString(), maxLength) : option.value}
                  {option.isDefault && <span style={{ fontSize: '12px', color: '#757575' }}>Default</span>}
                </Box>
              </li>
            ))}
            // open={open}
            renderInput={(params: any) => (
              <TextField
                {...params}
                label={props?.placeholder}
                inputRef={ref}
                error={Boolean(error)}
                // helperText={error ? '' : null}
                helperText={error ? error.message : null}
              />
            )}
          />
        );
      }}
    />
  );
};

RHFAutocompleteField.defaultProps = {
  placeholder: 'Label',
  popupIcon: <GrFormDown size={18} />,
  autoCompleteClasss: '',
  popperClass: '',
  id: '',
  rules: '',
  disableClearable: false,
  disabled: false,
  defaultValue: '',
  clearErrors: null,
  fullWidth: false,
  noOptionsText: 'No options available',
  renderOption: null, // ✅ Default to `null` to use normal behavior
  // open: false,
  truncateText: false, // Default is false
  maxLength: 25, //
  optionLabel: '',
};

export default RHFAutocompleteField;
