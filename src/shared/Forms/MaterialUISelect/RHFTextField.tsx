import { InputAdornment, TextField } from '@mui/material';
import * as React from 'react';
import { Controller } from 'react-hook-form';

interface RHFTextFieldProps {
  control: any;
  name: string;
  placeholder?: string;
  autocompleteClasses?: string;
  rules?: any;
  disabled?: boolean;
  clearErrors?: any;
  fullWidth?: boolean;
  sx?: any;
  defaultValue?: any;
  onlyNumbers?: boolean; // New prop to indicate only numeric input
  maxCharcters?: number,
  endIcon?: React.ReactNode; // New prop for end icon
}

const RHFTextField = (
  props: RHFTextFieldProps,
) => {
  const {
    control, name,
    placeholder = 'Label', rules,
    disabled = false,
    autocompleteClasses = '', clearErrors,
    fullWidth = false,
    sx = {},
    defaultValue = '',
    onlyNumbers = false,
    maxCharcters, endIcon,
  } = props;

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field, fieldState: { error } }) => {
        const { onChange, value, ref } = field;
        return (
          <TextField
            label={placeholder}
            sx={{
              ...sx,
              '.MuiFormHelperText-root': {
                fontSize: '12px !important',
              },
            }}
            value={value || ''}
            onChange={async ({ target: { value: newValue } }) => {
              const trimmedValue = await newValue;
              if (onlyNumbers) {
                const numericValue = trimmedValue.replace(/\D/g, '');
                await onChange(numericValue.slice(0, maxCharcters));
              } else {
                await onChange(trimmedValue);
              }
              if (clearErrors) {
                clearErrors(name);
              }
            }}
            onBlur={async () => {
              const trimmedValue = typeof value === "string" ? value.trim() : "";
              onChange(trimmedValue);
            }}
            fullWidth={fullWidth}
            inputRef={ref}
            className={autocompleteClasses}
            variant="outlined"
            disabled={disabled}
            defaultValue={defaultValue}
            // id={id}
            error={Boolean(error)}
            helperText={error ? error.message : null}
            // helperText={error ? '' : null}
            inputProps={{
              inputMode: onlyNumbers ? 'numeric' : 'text',
              pattern: onlyNumbers ? '[0-9]*' : undefined,
            }}
            InputProps={{
              endAdornment: endIcon ? (
                <InputAdornment className="textField-icon" position="end">{endIcon}</InputAdornment>
              ) : null,
            }}
          />
        );
      }}
    />
  );
};

RHFTextField.defaultProps = {
  placeholder: 'Label',
  autocompleteClasses: '',
  rules: '',
  disabled: false,
  clearErrors: null,
  sx: {},
  fullWidth: false,
  defaultValue: '',
  onlyNumbers: false,
  maxCharcters: 15,
  endIcon: null,
};

export default RHFTextField;
