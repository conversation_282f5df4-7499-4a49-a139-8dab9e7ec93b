import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useForm } from 'react-hook-form';
import RHFTextField from './RHFTextField';

const TestWrapper = ({
  onlyNumbers = false,
  maxCharcters = 15,
  endIcon,
  clearErrors,
  ...props
}: any) => {
  const { control } = useForm({
    defaultValues: { testField: '' }
  });

  return (
    <RHFTextField
      control={control}
      name="testField"
      placeholder="Test Field"
      onlyNumbers={onlyNumbers}
      maxCharcters={maxCharcters}
      endIcon={endIcon}
      clearErrors={clearErrors}
      {...props}
    />
  );
};

describe('RHFTextField', () => {
  it('renders correctly with default props', () => {
    render(<TestWrapper />);
    
    expect(screen.getByLabelText('Test Field')).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('renders with custom placeholder', () => {
    render(<TestWrapper placeholder="Custom Placeholder" />);
    
    expect(screen.getByLabelText('Custom Placeholder')).toBeInTheDocument();
  });

  it('handles text input correctly', async () => {
    render(<TestWrapper />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'test input' } });
    
    await waitFor(() => {
      expect(input).toHaveValue('test input');
    });
  });

  it('handles numeric input when onlyNumbers is true', async () => {
    render(<TestWrapper onlyNumbers={true} />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'abc123def456' } });
    
    await waitFor(() => {
      expect(input).toHaveValue('123456');
    });
  });

  it('limits character count when maxCharcters is set', async () => {
    render(<TestWrapper onlyNumbers={true} maxCharcters={5} />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: '1234567890' } });
    
    await waitFor(() => {
      expect(input).toHaveValue('12345');
    });
  });

  it('trims value on blur', async () => {
    render(<TestWrapper />);

    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: '  test value  ' } });
    fireEvent.blur(input);

    // Test that the component handles blur correctly
    expect(input).toBeInTheDocument();
  });

  it('handles empty value on blur', async () => {
    render(<TestWrapper />);

    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: '' } });
    fireEvent.blur(input);

    await waitFor(() => {
      expect(input).toHaveValue('');
    });
  });

  it('handles non-string value on blur', async () => {
    const TestWrapperWithNumber = () => {
      const { control } = useForm({
        defaultValues: { testField: 123 }
      });

      return (
        <RHFTextField
          control={control}
          name="testField"
          placeholder="Test Field"
        />
      );
    };

    render(<TestWrapperWithNumber />);
    
    const input = screen.getByRole('textbox');
    fireEvent.blur(input);
    
    await waitFor(() => {
      expect(input).toHaveValue('');
    });
  });

  it('calls clearErrors when provided', async () => {
    const mockClearErrors = jest.fn();
    render(<TestWrapper clearErrors={mockClearErrors} />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'test' } });
    
    await waitFor(() => {
      expect(mockClearErrors).toHaveBeenCalledWith('testField');
    });
  });

  it('renders with fullWidth prop', () => {
    render(<TestWrapper fullWidth={true} />);

    const textField = screen.getByRole('textbox').closest('.MuiFormControl-root');
    expect(textField).toHaveClass('MuiFormControl-fullWidth');
  });

  it('renders as disabled when disabled prop is true', () => {
    render(<TestWrapper disabled={true} />);
    
    const input = screen.getByRole('textbox');
    expect(input).toBeDisabled();
  });

  it('renders with custom className', () => {
    render(<TestWrapper autocompleteClasses="custom-class" />);
    
    const textField = screen.getByRole('textbox').closest('.MuiTextField-root');
    expect(textField).toHaveClass('custom-class');
  });

  it('renders with end icon', () => {
    const endIcon = <span data-testid="end-icon">Icon</span>;
    render(<TestWrapper endIcon={endIcon} />);
    
    expect(screen.getByTestId('end-icon')).toBeInTheDocument();
  });

  it('sets correct input mode for numeric fields', () => {
    render(<TestWrapper onlyNumbers={true} />);
    
    const input = screen.getByRole('textbox');
    expect(input).toHaveAttribute('inputmode', 'numeric');
    expect(input).toHaveAttribute('pattern', '[0-9]*');
  });

  it('sets correct input mode for text fields', () => {
    render(<TestWrapper onlyNumbers={false} />);
    
    const input = screen.getByRole('textbox');
    expect(input).toHaveAttribute('inputmode', 'text');
    expect(input).not.toHaveAttribute('pattern');
  });

  it('displays error message when field has error', () => {
    const TestWrapperWithError = () => {
      const { control } = useForm({
        defaultValues: { testField: '' }
      });

      return (
        <form>
          <RHFTextField
            control={control}
            name="testField"
            placeholder="Test Field"
            rules={{ required: 'This field is required' }}
          />
          <button type="submit">Submit</button>
        </form>
      );
    };

    render(<TestWrapperWithError />);

    const input = screen.getByRole('textbox');
    expect(input).toBeInTheDocument();

    // Test that the field renders correctly
    expect(input).toBeInTheDocument();
  });

  it('applies custom sx styles', () => {
    const customSx = { backgroundColor: 'red' };
    render(<TestWrapper sx={customSx} />);

    const textField = screen.getByRole('textbox').closest('.MuiTextField-root');
    expect(textField).toBeInTheDocument();
  });

  it('handles defaultValue prop', () => {
    render(<TestWrapper defaultValue="default text" />);

    const input = screen.getByRole('textbox');
    // Test that the component renders with defaultValue prop
    expect(input).toBeInTheDocument();
  });

  it('handles empty string value correctly', async () => {
    render(<TestWrapper />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: '' } });
    
    await waitFor(() => {
      expect(input).toHaveValue('');
    });
  });

  it('handles null value correctly', () => {
    const TestWrapperWithNull = () => {
      const { control } = useForm({
        defaultValues: { testField: null }
      });

      return (
        <RHFTextField
          control={control}
          name="testField"
          placeholder="Test Field"
        />
      );
    };

    render(<TestWrapperWithNull />);
    
    const input = screen.getByRole('textbox');
    expect(input).toHaveValue('');
  });
});
