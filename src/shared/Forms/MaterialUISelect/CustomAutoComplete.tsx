import React from 'react';
import TextField from '@material-ui/core/TextField';
import { GrFormDown } from 'react-icons/gr';
import { Autocomplete } from '@mui/material';

export interface Option {
  value: string;
}

interface Props {
  value: Option | null;
  options: Option[];
  onChange: (event: React.ChangeEvent<object>, value: Option | null) => void;
  getOptionLabel: (option: Option) => string;
  label?: string;
  variant?: 'standard' | 'outlined' | 'filled';
  disableClearable?: boolean;
  disabled?: boolean;
  popupIcon?: React.ReactNode;
  autoCompleteClasss?: string;
  name?: string;
}

const CustomAutocomplete: React.FC<Props> = ({
  value,
  options,
  onChange,
  getOptionLabel,
  label,
  variant,
  disableClearable = false,
  disabled = false,
  popupIcon,
  autoCompleteClasss = '',
  name,
}) => (
  <Autocomplete
    sx={{
      marginTop: '24px',
    }}
    className={`customAutoComplete ${autoCompleteClasss}`}
    disableClearable={disableClearable}
    disabled={disabled}
    value={value || null}
    options={options}
    popupIcon={popupIcon || <GrFormDown size={18} />}
    onChange={onChange}
    getOptionLabel={getOptionLabel}
    renderInput={(params: any) => (
      <TextField
        {...params}
        name={name}
        label={label || 'Label'}
        variant={variant || 'outlined'}
      />
    )}
  />
);

CustomAutocomplete.defaultProps = {
  label: 'Label',
  variant: 'outlined',
  disableClearable: false,
  disabled: false,
  popupIcon: <GrFormDown size={18} />,
  autoCompleteClasss: '',
  name: '',
};

export default CustomAutocomplete;
