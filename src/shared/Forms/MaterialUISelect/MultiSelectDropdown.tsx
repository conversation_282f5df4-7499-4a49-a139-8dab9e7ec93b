import React from 'react';
import {
  FormControl, InputLabel,
  MenuItem,
  Select,
  makeStyles,
} from '@material-ui/core';

interface Option {
  value: string;
  label: string;
}

interface Props {
  options: Option[];
  selectedValues: string[];
  handleChange: (event: React.ChangeEvent<{ value: unknown }>) => void;
}

const useStyles = makeStyles((theme) => ({
  formControl: {
    margin: theme.spacing(1),
    fullWidth: true,
  },
}));

const MultiSelectDropdown: React.FC<Props> = ({ options, selectedValues, handleChange }) => {
  const classes = useStyles();

  return (
    <FormControl fullWidth className={classes.formControl}>
      <InputLabel id="multiple-select-label">Select</InputLabel>
      <Select
        labelId="multiple-select-label"
        id="multiple-select"
        // multiple
        value={selectedValues}
        onChange={handleChange}
      >
        {options.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            {option.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default MultiSelectDropdown;
