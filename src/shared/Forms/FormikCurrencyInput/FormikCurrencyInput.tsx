import React, { useEffect, useState } from 'react';
import { TextFieldProps, TextField, InputAdornment } from '@mui/material';
import { useField } from 'formik';

import usePropagateRef from 'shared/Forms/usePropagateRef';
import CurrencyInput from 'shared/CurrencyInput';

export type FormikCurrencyInputProps = Omit<TextFieldProps, 'name'> & {
  readOnly?:boolean
  name: string
  disablePerformance?: boolean | undefined
  loading?: boolean | undefined
  className: string | undefined
  currencySign?: string
  decimalScale?: number
};

const decimalSeparator = '.';

export const formatCurrencyValue = (value: string | number, decimalScale = 2) => {
  if (value === 0) return '0.00';

  if (!value) return '';

  const [integer, decimal = ''] = String(value).split(decimalSeparator);
  return `${integer}.${decimal.padEnd(decimalScale, '0')}`;
};

export const formatDecimalValue = (value :string| number, minLen = 2, maxLen = 2) => {
  const formattedValue = value.toLocaleString(undefined, {
    minimumFractionDigits: minLen,
    maximumFractionDigits: maxLen,
  });
  return formattedValue;
};
const FormikCurrencyInput = (props: FormikCurrencyInputProps) => {
  const {
    className, name, disablePerformance, loading, currencySign, decimalScale, InputProps, readOnly, ...otherProps
  } = props;

  const [field, meta] = useField(name);
  const error = !!meta.error && meta.touched;
  const [fieldValue, setFieldValue] = useState<string | number>(formatCurrencyValue(field.value, decimalScale));

  usePropagateRef({
    setFieldValue,
    name,
    value: field.value,
  });

  useEffect(() => {
    if (meta.touched) {
      return;
    }

    if (field.value !== fieldValue) {
      setFieldValue(formatCurrencyValue(field.value, decimalScale));
    }
  }, [field.value]);

  const onChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    setFieldValue(evt.target.value);
  };
  const onBlur = (evt: React.FocusEvent<HTMLInputElement>) => {
    const val = formatCurrencyValue(evt.target.value, decimalScale);

    setFieldValue(val);

    field.onChange({
      target: {
        name,
        value: val,
      },
    });
  };

  const performanceProps = disablePerformance
    ? {
      ...field,
      value: loading ? 'Loading...' : fieldValue,
      onBlur,
    }
    : {
      ...field,
      value: loading ? 'Loading...' : fieldValue,
      onChange,
      onBlur,
      onFocus: onBlur,
    };

  return (
    <TextField
      {...otherProps}
      className={`text-field ${className}`}
      data-testid="currency_field"
      autoComplete="off"
      InputProps={{
        ...InputProps,
        inputComponent: CurrencyInput,
        startAdornment: currencySign && <InputAdornment position="start">{currencySign}</InputAdornment>,
        inputProps: {
          ...InputProps?.inputProps,
          decimalScale,
          allowedDecimalSeparators: [decimalSeparator],
          readOnly: readOnly || false,

        },
      }}
      error={error}
      {...performanceProps}
    />
  );
};

FormikCurrencyInput.defaultProps = {
  disablePerformance: false,
  loading: false,
  currencySign: undefined,
  decimalScale: 2,
  readOnly: false,
};

export default FormikCurrencyInput;
