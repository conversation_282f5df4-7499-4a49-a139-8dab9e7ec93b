import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react';
import { Formik } from 'formik';
import FormikCurrencyInput, { formatDecimalValue } from './FormikCurrencyInput';

const testRender = (testField = '', decimalScale) => render(
  <Formik initialValues={{ testField }}>
    <FormikCurrencyInput
      name="testField"
      label="Test Field"
      decimalScale={decimalScale}
    />
  </Formik>,
);

const inputTestId = 'currency-input';

describe('FormikCurrencyInput', () => {
  it('should renders correctly', () => {
    const { getByTestId } = testRender();
    expect(getByTestId(inputTestId)).toBeInTheDocument();
  });

  it('should keep default value as empty sting', async () => {
    const { getByTestId } = testRender();
    const input = getByTestId(inputTestId);
    await waitFor(() => expect(input.value).toBe(''));
  });
  it('should add 1.233 return value be', () => {
    const result = formatDecimalValue(1.233);
    expect(result).toBe('1.23');
  });
  it('should add 1000 return value be', () => {
    const result = formatDecimalValue(1000);
    expect(result).toBe('1,000.00');
  });
  it('should add 1.678 return value be', () => {
    const result = formatDecimalValue(1.678);
    expect(result).toBe('1.68');
  });
  it('should add .00 to value if value has no decimal part', async () => {
    const { getByTestId } = testRender();

    const input = getByTestId(inputTestId);
    const value = '123';
    fireEvent.change(input, { target: { value } });
    await waitFor(() => expect(input.value).toBe(`${value}.00`));
  });

  it('should add 0 to the decimal part if decimal part has 1 sign', async () => {
    const { getByTestId } = testRender();

    const input = getByTestId(inputTestId);
    const value = '123.4';
    fireEvent.change(input, { target: { value } });
    await waitFor(() => expect(input.value).toBe(`${value}0`));
  });

  it('should not change value if decimal part already has 2 signs', async () => {
    const { getByTestId } = testRender();

    const input = getByTestId(inputTestId);
    const value = '123.45';
    fireEvent.change(input, { target: { value } });
    await waitFor(() => expect(input.value).toBe(value));
  });

  it('should adjust default value by adding .00', async () => {
    const value = 1;
    const { getByTestId } = testRender(value);

    const input = getByTestId(inputTestId);
    await waitFor(() => expect(input.value).toBe(`${value}.00`));
  });

  it('should adjust default value by adding 0', async () => {
    const value = 1.1;
    const { getByTestId } = testRender(value);

    const input = getByTestId(inputTestId);
    await waitFor(() => expect(input.value).toBe(`${value}0`));
  });

  it('should adjust default value by converting it to string', async () => {
    const value = 1.12;
    const { getByTestId } = testRender(value);

    const input = getByTestId(inputTestId);
    await waitFor(() => expect(input.value).toBe(`${value}`));
  });

  it('should add 00 to the value as decimal scale is 4', async () => {
    const value = 1.12;
    const decimalScale = 4;
    const { getByTestId } = testRender(value, decimalScale);

    const input = getByTestId(inputTestId);
    await waitFor(() => expect(input.value).toBe(`${value}00`));
  });

  it('should remove all decimals that is more then decimal scale', async () => {
    const value = 1.1234444;
    const decimalScale = 4;
    const { getByTestId } = testRender(value, decimalScale);

    const input = getByTestId(inputTestId);
    await waitFor(() => expect(input.value).toBe('1.1234'));
  });
});
