import React from 'react';
import { Formik } from 'formik';
import { render, fireEvent } from '@testing-library/react';
import FormikSelectField from './FormikSelectField';

const options = [
  { value: 'option1', title: 'Option 1' },
  { value: 'option2', title: 'Option 2' },
];
const label = 'Select an option';
const name = 'testField';

describe('FormikSelectField', () => {
  it('render label', () => {
    const { getByTestId } = render(
      <Formik initialValues={{ testField: '' }} onSubmit={() => {}}>
        <FormikSelectField options={options} label={label} name={name} />
      </Formik>,
    );
    const select = getByTestId('select-label');
    expect(select).toBeInTheDocument();
    expect(select.innerHTML).toBe(label);
  });

  it('render correct label', () => {
    const { getByTestId } = render(
      <Formik initialValues={{ testField: '' }} onSubmit={() => {}}>
        <FormikSelectField options={options} label={label} name={name} />
      </Formik>,
    );
    const select = getByTestId('select-label');
    expect(select.innerHTML).toBe(label);
  });

  it('handles changes correctly', () => {
    const onChange = jest.fn();
    const { getByTestId } = render(
      <Formik initialValues={{ testField: 'option1' }} onSubmit={() => {}}>
        <FormikSelectField options={options} label={label} name={name} onChange={onChange} />
      </Formik>,
    );
    const select = getByTestId('select-field');
    fireEvent.change(select, { target: { value: 'option2' } });
    expect(onChange).toHaveBeenCalled();
  });
});
