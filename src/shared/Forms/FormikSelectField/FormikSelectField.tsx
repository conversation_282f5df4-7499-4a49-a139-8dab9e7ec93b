import {
  FormControl,
  InputLabel,
  MenuItem, Select,
} from '@mui/material';
import { useField } from 'formik';
import React from 'react';
import { GrFormDown } from 'react-icons/gr';
import { SelectOptionType } from 'shared/Forms/Forms.models';
import './FormikSelectField.scss';

interface IFormikSelectFieldProps {
  name: string,
  options?: SelectOptionType[],
  label?: string,
  className?: string,
  defaultValue?: string,
  disabled?: boolean,
}

const FormikSelectField: React.FC<IFormikSelectFieldProps> = ({
  options, label, name, className, disabled, ...rest
}) => {
  const [{ value }, { error, touched }, { setValue }] = useField(name);
  const isError = !!error && touched;

  return (
    <FormControl variant="outlined" className={`select ${className || 'w100'}`} aria-labelledby={name} data-testid="select">
      <InputLabel data-testid="select-label">{label}</InputLabel>
      <Select
        IconComponent={() => (<GrFormDown className="select-arrow-icon" size={21} />)}
        name={name}
        label={label}
        value={value}
        onChange={(e) => setValue(e.target.value)}
        className="select-field"
        inputProps={{
          'data-testid': 'select-field',
          readOnly: disabled || false,
          ...(disabled && {
            IconComponent: () => null,
          }),

        }}
        error={isError}
        {...rest}
      >
        {options && options.map((option) => (
          <MenuItem value={option.value} key={option.title}>{option.title}</MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

FormikSelectField.defaultProps = {
  className: '',
  defaultValue: '',
  disabled: false,
  label: '',
  options: [{ value: undefined, title: '' }],
};

export default FormikSelectField;
