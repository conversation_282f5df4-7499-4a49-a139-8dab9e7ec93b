import React from 'react';
import { AiOutlineClose } from 'react-icons/ai';
import { Autocomplete, TextField } from '@mui/material';
import { GrFormDown } from 'react-icons/gr';

import { SelectOptionType } from 'shared/Forms/Forms.models';
import './MultiSelectField.scss';

interface IMultiSelectFieldProps {
  autoComplete?: boolean;
  label?: string;
  name: string;
  onChange: any;
  options: SelectOptionType[]|unknown;
  placeholder?: string;
  selectOnFocus?: boolean;
  value?: any;
  readOnly?: boolean;
  disable?:boolean
}
const MultiSelectField = ({
  name, label, value, placeholder, options, autoComplete, selectOnFocus, disable, onChange, ...rest
}: IMultiSelectFieldProps) => (
  <Autocomplete
    sx={{
      '& .MuiAutocomplete-endAdornment': {
        ...(disable && {
          display: 'none',
        }),
      },
    }}
    className="multi-select-field"
    autoComplete={autoComplete || false}
    data-testid="multi-select-field"
    disableClearable
    disableCloseOnSelect
    getOptionLabel={(option) => option?.title || ''}
    limitTags={1}
    multiple
    onChange={onChange}
    options={options as SelectOptionType[]}
    popupIcon={<GrFormDown className="select-arrow-icon" size={17} />}
    selectOnFocus={selectOnFocus || false}
    value={value}
    ChipProps={{
      deleteIcon: <AiOutlineClose size={12} />,
    }}
    renderOption={(props, option) => <span {...props}>{option.title}</span>}
    renderInput={(params) => (
      <>
        {/* // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore */}
        <TextField
          name={name}
          variant="outlined"
          placeholder={placeholder}
          label={label}
          {...params}
        />
      </>

    )}
    {...rest}
  />
);

MultiSelectField.defaultProps = {
  disable: false,
  autoComplete: false,
  label: '',
  placeholder: '',
  readOnly: false,
  selectOnFocus: false,
  value: [],
};

export default MultiSelectField;
