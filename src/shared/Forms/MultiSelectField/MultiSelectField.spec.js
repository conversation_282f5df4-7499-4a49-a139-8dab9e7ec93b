import React from 'react';
import { render } from '@testing-library/react';

import MultiSelectField from './MultiSelectField';

const mockedProps = {
  name: 'note',
  label: 'Note',
  placeholder: 'Enter note',
  options: [
    {
      title: 'The Lord of the Rings: The Return of the King',
      year: 2003,
    },
    { title: 'Interstellar', year: 2014 },
  ],
};

describe('Forms: MultiSelectField', () => {
  test('should render "multi-select-field"', () => {
    const { getByTestId } = render(<MultiSelectField {...mockedProps} />);
    const content = getByTestId('multi-select-field');

    expect(content).toBeInTheDocument();
  });
});
