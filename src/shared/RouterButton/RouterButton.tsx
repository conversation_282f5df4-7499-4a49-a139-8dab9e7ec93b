import React, { ReactNode } from 'react';
import { ReactElementLike, ReactNodeLike } from 'prop-types';
import { Link } from 'react-router-dom';

import Button from '@mui/material/Button';

interface IRouterButtonProps {
  path: string;
  className?: string;
  children?: ReactNodeLike[] | ReactElementLike[] | ReactNode;
}

const RouterButton = ({
  className, path, children, ...props
}: IRouterButtonProps) => (
  <Link to={path} {...props} className={className}>
    <Button>{React.Children.only(children)}</Button>
  </Link>
);

RouterButton.defaultProps = {
  className: '',
  children: '',
};

export default RouterButton;
