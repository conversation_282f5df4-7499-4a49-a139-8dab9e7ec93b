import React from 'react';
import testRender from 'core/utilities/testUtils';
import RouterButton from './RouterButton';

const defaultMockProps = {
  path: 'path',
  children: <span>Go To</span>,
};

describe('RouterButton', () => {
  test('should render button children', () => {
    const mockProps = {
      ...defaultMockProps,
    };
    const { getByText } = testRender(<RouterButton {...mockProps} />);
    const buttonChildren = getByText('Go To');

    expect(buttonChildren).toBeInTheDocument();
  });
});
