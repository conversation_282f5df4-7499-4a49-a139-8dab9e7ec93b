import React from 'react';
import { render, screen } from '@testing-library/react';
import Batch from './Batch';

describe('Batch component', () => {
  test('renders correctly with "Usage Monitoring" label', () => {
    render(<Batch label="Usage Monitoring" />);
    const element = screen.getByText('Usage Monitoring');
    expect(element).toBeInTheDocument();
    expect(element).toHaveStyle('background-color: #A56DD7');
  });

  test('renders correctly with "SIM Provisioning" label', () => {
    render(<Batch label="SIM Provisioning" />);
    const element = screen.getByText('SIM Provisioning');
    expect(element).toBeInTheDocument();
    expect(element).toHaveStyle('background-color: #4EB6E1');
  });

  test('renders correctly with "Network and System" label', () => {
    render(<Batch label="Network and System" />);
    const element = screen.getByText('Network and System');
    expect(element).toBeInTheDocument();
    expect(element).toHaveStyle('background-color: #F18F93');
  });

  test('renders correctly with "Security" label', () => {
    render(<Batch label="Security" />);
    const element = screen.getByText('Security');
    expect(element).toBeInTheDocument();
    expect(element).toHaveStyle('background-color: #6EC9A7');
  });

  test('renders correctly with "Subscription Management" label', () => {
    render(<Batch label="Subscription Management" />);
    const element = screen.getByText('Subscription Management');
    expect(element).toBeInTheDocument();
    expect(element).toHaveStyle('background-color: #6F37BF');
  });

  test('renders correctly with "Frequently Used" label', () => {
    render(<Batch label="Frequently Used" />);
    const element = screen.getByText('Frequently Used');
    expect(element).toBeInTheDocument();
    expect(element).toHaveStyle('background-color: #707');
  });

  test('renders correctly with "N/A" label', () => {
    render(<Batch label="N/A" />);
    const element = screen.getByText('N/A');
    expect(element).toBeInTheDocument();
    expect(element).toHaveStyle('background-color: #BCBDC');
  });

  test('renders correctly with undefined or "initial" label', () => {
    render(<Batch label="initial" />);
    const element = screen.getByText('initial');
    expect(element).toBeInTheDocument();
    expect(element).toHaveStyle('background-color: transparent');
  });

  test('renders correctly with undefined label', () => {
    render(<Batch label="" />);
    const element = screen.queryByText('undefined');
    expect(element).not.toBeInTheDocument();
  });

  test('renders correctly with unlisted label', () => {
    render(<Batch label="Unlisted Label" />);
    const element = screen.getByText('Unlisted Label');
    expect(element).toBeInTheDocument();
    expect(element).toHaveStyle('background-color: gray');
  });
});
