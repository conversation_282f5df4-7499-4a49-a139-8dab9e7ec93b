import React from 'react';

import './ToastWithLink.scss';

type ToastWithLinkProps = { text: string; link: string; };

const ToastWithLink: React.FC<ToastWithLinkProps> = ({ text, link }) => (
  <div className="toast-with-link" data-testid="toast-with-link">
    <p data-testid="toast-with-link_text">{text}</p>
    <a href={link} className="toast-with-link_link">Here is a link.</a>
  </div>
);

export default ToastWithLink;
