import React from 'react';
import { render } from '@testing-library/react';

import Toast<PERSON>ithLink from './ToastWithLink';

const mockedProps = {
  text: 'Test text',
  link: '/',
};

describe('ToastWithLink', () => {
  test('should render "toast-with-link"', () => {
    const { getByTestId } = render(<ToastWithLink {...mockedProps} />);
    const content = getByTestId('toast-with-link');

    expect(content).toBeInTheDocument();
  });

  test('should render "toast-with-link"', () => {
    const { getByTestId } = render(<ToastWithLink {...mockedProps} />);
    const content = getByTestId('toast-with-link_text');

    expect(content.innerHTML).toBe('Test text');
  });
});
