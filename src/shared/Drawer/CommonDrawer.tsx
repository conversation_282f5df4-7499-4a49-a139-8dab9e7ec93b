import React from 'react';
import { Drawer, makeStyles } from '@material-ui/core';
import RuleDrawer from 'features/AutomationRules/RuleDrawer';

interface CommonDrawerProps {
  className?: string;
  children: React.ReactNode;
  anchor: 'left' | 'right' | 'top' | 'bottom';
  onClose: () => void;
  open: boolean;
  heading: string;
  variant?: 'permanent' | 'persistent' | 'temporary';
}

const useStyles = makeStyles({
  drawerPaper: {
    width: 540,
  },
});

const CommonDrawer: React.FC<CommonDrawerProps> = ({
  className = 'drawer',
  children,
  anchor = 'left',
  onClose,
  open,
  heading,
  variant = 'temporary',
}) => {
  const classes = useStyles();
  return (
    <Drawer
      className={className}
      anchor={anchor}
      open={open}
      variant={variant}
      onClose={onClose}
      classes={{
        paper: classes.drawerPaper,
      }}
      data-testid="drawer"
    >
      <RuleDrawer heading={heading} close={() => onClose()} />
      {children}
    </Drawer>
  );
};

CommonDrawer.defaultProps = {
  className: 'drawer',
  variant: 'temporary',
};

export default CommonDrawer;
