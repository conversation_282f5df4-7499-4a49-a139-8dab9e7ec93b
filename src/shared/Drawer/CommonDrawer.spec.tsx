// CommonDrawer.test.tsx
import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CommonDrawer from './CommonDrawer';

describe('CommonDrawer component', () => {
  const theme = createTheme();

  const renderWithTheme = (ui: React.ReactElement) => render(<ThemeProvider theme={theme}>{ui}</ThemeProvider>);

  test('renders with correct heading and children', () => {
    renderWithTheme(
      <CommonDrawer
        open
        onClose={jest.fn()}
        heading="Test Heading"
        anchor="left"
      >
        <div data-testid="drawer-children">Drawer Content</div>
      </CommonDrawer>,
    );

    expect(screen.getByText('Test Heading')).toBeInTheDocument();
    expect(screen.getByTestId('drawer-children')).toHaveTextContent('Drawer Content');
  });

  test('applies the correct classes', () => {
    renderWithTheme(
      <CommonDrawer
        open
        onClose={jest.fn()}
        heading="Test Heading"
        anchor="left"
        className="custom-drawer"
      >
        <div>Drawer Content</div>
      </CommonDrawer>,
    );

    const drawer = screen.getByTestId('drawer');
    expect(drawer).toHaveClass('custom-drawer');
    expect(drawer.querySelector('.MuiDrawer-paper')).toHaveStyle({ width: '540px' });
  });
});
