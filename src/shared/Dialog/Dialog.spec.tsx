import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import Dialog from './Dialog';

// Mock the AppContextProvider
jest.mock('AppContextProvider', () => ({
  useAppContext: () => ({
    currentTheme: {
      primaryColor: '#5514B4',
    },
  }),
}));

// Mock the CloseIcon component
jest.mock('assets/images/CloseIcon', () => {
  return function MockCloseIcon() {
    return <span data-testid="close-icon">×</span>;
  };
});

describe('Dialog', () => {
  const theme = createTheme();
  const defaultProps = {
    open: true,
    title: 'Test Dialog',
    onClose: jest.fn(),
  };

  const renderWithTheme = (ui: React.ReactElement) => 
    render(<ThemeProvider theme={theme}>{ui}</ThemeProvider>);

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders dialog when open is true', () => {
    renderWithTheme(<Dialog {...defaultProps} />);
    
    expect(screen.getByText('Test Dialog')).toBeInTheDocument();
    expect(screen.getByRole('dialog')).toBeInTheDocument();
  });

  it('does not render dialog when open is false', () => {
    renderWithTheme(<Dialog {...defaultProps} open={false} />);
    
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  it('renders title correctly', () => {
    renderWithTheme(<Dialog {...defaultProps} title="Custom Title" />);
    
    expect(screen.getByText('Custom Title')).toBeInTheDocument();
  });

  it('renders subtitle when provided', () => {
    renderWithTheme(
      <Dialog {...defaultProps} title="Main Title" subTitle="Sub Title" />
    );
    
    expect(screen.getByText('Main Title')).toBeInTheDocument();
    expect(screen.getByText('Sub Title')).toBeInTheDocument();
  });

  it('renders children content', () => {
    renderWithTheme(
      <Dialog {...defaultProps}>
        <div data-testid="dialog-content">Dialog Content</div>
      </Dialog>
    );
    
    expect(screen.getByTestId('dialog-content')).toBeInTheDocument();
    expect(screen.getByText('Dialog Content')).toBeInTheDocument();
  });

  it('renders footer children', () => {
    const footerContent = (
      <button data-testid="footer-button">Footer Button</button>
    );
    
    renderWithTheme(
      <Dialog {...defaultProps} footerchildren={footerContent} />
    );
    
    expect(screen.getByTestId('footer-button')).toBeInTheDocument();
  });

  it('calls onClose when close icon is clicked', () => {
    const mockOnClose = jest.fn();
    renderWithTheme(<Dialog {...defaultProps} onClose={mockOnClose} />);
    
    const closeButton = screen.getByRole('button');
    fireEvent.click(closeButton);
    
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('does not call onClose when backdrop is clicked', () => {
    const mockOnClose = jest.fn();
    renderWithTheme(<Dialog {...defaultProps} onClose={mockOnClose} />);

    const backdrop = document.querySelector('.MuiBackdrop-root');
    if (backdrop) {
      fireEvent.click(backdrop);
    }

    expect(mockOnClose).not.toHaveBeenCalled();
  });

  it('does not call onClose when escape key is pressed', () => {
    const mockOnClose = jest.fn();
    renderWithTheme(<Dialog {...defaultProps} onClose={mockOnClose} />);
    
    fireEvent.keyDown(screen.getByRole('dialog'), { key: 'Escape' });
    
    expect(mockOnClose).not.toHaveBeenCalled();
  });

  it('calls onClose for other close reasons', () => {
    const mockOnClose = jest.fn();
    const TestDialog = () => {
      const [open, setOpen] = React.useState(true);
      
      const handleClose = (event: any, reason: string) => {
        if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) { 
          return; 
        }
        mockOnClose();
        setOpen(false);
      };
      
      return (
        <Dialog 
          open={open} 
          title="Test" 
          onClose={() => handleClose({}, 'other')} 
        />
      );
    };
    
    renderWithTheme(<TestDialog />);
    
    const closeButton = screen.getByRole('button');
    fireEvent.click(closeButton);
    
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('renders without title section when title is not provided', () => {
    renderWithTheme(
      <Dialog open={true} title="" onClose={jest.fn()}>
        <div>Content</div>
      </Dialog>
    );
    
    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  it('applies custom props to MuiDialog', () => {
    renderWithTheme(
      <Dialog 
        {...defaultProps} 
        maxWidth="md"
        fullWidth
        data-testid="custom-dialog"
      />
    );
    
    const dialog = screen.getByRole('dialog');
    expect(dialog.closest('.MuiDialog-root')).toBeInTheDocument();
  });

  it('uses default props correctly', () => {
    renderWithTheme(<Dialog {...defaultProps} />);
    
    // Test that default props are applied (empty strings for optional props)
    expect(screen.getByRole('dialog')).toBeInTheDocument();
  });

  it('handles theme styling correctly', () => {
    renderWithTheme(<Dialog {...defaultProps} />);
    
    const titleContainer = screen.getByText('Test Dialog').closest('div');
    expect(titleContainer).toHaveStyle('border-top: 6px solid #5514B4');
  });

  it('renders close icon correctly', () => {
    renderWithTheme(<Dialog {...defaultProps} />);
    
    expect(screen.getByTestId('close-icon')).toBeInTheDocument();
  });

  it('handles dialog content styling', () => {
    renderWithTheme(
      <Dialog {...defaultProps}>
        <div>Test Content</div>
      </Dialog>
    );
    
    const content = screen.getByText('Test Content').closest('.dialogueContent_sim');
    expect(content).toBeInTheDocument();
  });

  it('handles dialog actions styling', () => {
    const footerContent = <button>Test Button</button>;
    
    renderWithTheme(
      <Dialog {...defaultProps} footerchildren={footerContent} />
    );
    
    const actions = screen.getByText('Test Button').closest('.MuiDialogActions-root');
    expect(actions).toBeInTheDocument();
  });
});
