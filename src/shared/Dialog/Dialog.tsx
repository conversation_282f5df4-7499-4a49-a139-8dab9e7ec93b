import {
  Box,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  IconButton,
  Dialog as MuiDialog,
  Typography,
} from '@mui/material';
import { useAppContext } from 'AppContextProvider';
import CloseIcon from 'assets/images/CloseIcon';
import React from 'react';

interface IDialog extends DialogProps {

  open: boolean,
  title: string,
  subTitle?: string,
  onClose: () => void,

  children?: React.ReactNode,
  footerchildren?: React.ReactNode,
  formClass?: string,
}
const Dialog = ({
  open, title, subTitle,
  onClose, children, footerchildren, ...props
}: IDialog,
) => {

  const { currentTheme } = useAppContext();
  const oncloseModal = (event, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) { return; }
    onClose();
  };
  return (
    <MuiDialog
      open={open}
      onClose={oncloseModal}
      aria-labelledby="edit-apartment"
      {...props}
    >

      {title && (
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          sx={{
            borderTop: `6px solid ${currentTheme?.primaryColor} `,
            '& .MuiDialogTitle-root': {
              padding: '26px 32px !important',
            },
          }}
        >
          <DialogTitle
            id="display-dialog"
          >
            <Typography variant="h3">{title}</Typography>
            <Typography component="div" variant="subtitle1">{subTitle}</Typography>
          </DialogTitle>
          <IconButton
            sx={{
              marginRight: '32px',
            }}
            className="closeIcon"
            onClick={() => onClose()}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      )}
      <DialogContent className="dialogueContent_sim" sx={{ padding: '0px 32px' }} style={{ overflow: 'hidden' }}>
        {children}
      </DialogContent>
      <DialogActions sx={{ padding: '0px 32px 32px', justifyContent: 'flex-start' }}>
        {footerchildren}
      </DialogActions>
    </MuiDialog>
  );
};
export default Dialog;

Dialog.defaultProps = {
  footerchildren: '',
  children: '',
  formClass: '',
  subTitle: '',
};
