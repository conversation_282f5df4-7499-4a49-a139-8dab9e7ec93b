import React, { ChangeEvent, FC, useState } from 'react';
import { AiOutlineSearch } from 'react-icons/ai';
import { InputBase, styled, Tooltip } from '@mui/material';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import useDebounce from 'shared/useDebounce';
import './SearchInput.scss';

const SearchWrapper = styled('div')(({ theme }) => ({
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${styles.inputBorderColor}`,
  '&:focus-within': {
    border: `1px solid ${theme.palette.primary.main}`,
  },
}));


const StyledInputBase = styled(InputBase)(({ theme }) => ({
  color: 'inherit',
  width: '100%',
  height: '40px',
  '& .MuiInputBase-input': {
    padding: theme.spacing(1, 1, 1, 0),
    fontSize: '14px',
    paddingLeft: `calc(1em + ${theme.spacing(4)})`,
    width: '100%',
  },
}));

interface ISearchInputProps {
  placeholder: string;
  className?: string;

  onChange: (event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => void;
  tooltip?: string;
  value?: string;
}

const SearchInput: FC<ISearchInputProps> = ({
  placeholder,
  className,
  onChange,
  tooltip,
  value = '',
}) => {
  const [searchTerm, setSearchTerm] = useState<string>(value);
  const debouncedSearch = useDebounce(onChange, 1000);
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue: any = e.target?.value;
    setSearchTerm(searchValue);
    // Call the debounced onSearch function with the updated search term
    debouncedSearch(searchValue);
  };
  return (
    <SearchWrapper className={`search ${className}`} data-testid="search-input">
      <Tooltip title={tooltip} arrow placement="bottom" data-testid="search-tooltip">
        <div>
          <div className="search__icon-wrapper">
            <AiOutlineSearch size={20} color={styles.darkColor300} />
          </div>
          <StyledInputBase
            placeholder={placeholder}
            inputProps={{ 'aria-label': 'search' }}
            value={searchTerm}
            onChange={handleChange}
            autoFocus
          />
        </div>
      </Tooltip>
    </SearchWrapper>
  );
};

SearchInput.defaultProps = {
  className: '',
  tooltip: '',
  value: '',
};

export default SearchInput;
