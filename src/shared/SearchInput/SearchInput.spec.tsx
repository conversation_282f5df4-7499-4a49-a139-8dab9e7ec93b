import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import testRender from 'core/utilities/testUtils';
import SearchInput from './SearchInput';

const tooltipText = 'Tooltip';

describe('SearchInput', () => {
  test('should SearchInput correct render', () => {
    testRender(<SearchInput placeholder="Search" onChange={() => undefined} />);

    expect(screen.getByPlaceholderText('Search')).toBeInTheDocument();
  });

  test('should render tooltip on hover', () => {
    const { getByTestId } = testRender(<SearchInput placeholder="Search" onChange={() => true} tooltip={tooltipText} />);

    const wrapper = getByTestId('search-input');

    userEvent.hover(wrapper);

    waitFor(() => {
      expect(getByTestId('search-tooltip')).toBeInTheDocument();
    });
  });

  test('should render tooltip with text "Tooltip"', () => {
    const { getByTestId, getByText } = testRender(<SearchInput placeholder="Search" onChange={() => true} tooltip={tooltipText} />);

    const wrapper = getByTestId('search-input');

    userEvent.hover(wrapper);

    waitFor(() => {
      expect(getByText(tooltipText)).toBeInTheDocument();
    });
  });
});
