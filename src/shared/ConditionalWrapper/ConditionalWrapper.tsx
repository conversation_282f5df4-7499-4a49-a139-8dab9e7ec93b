import React, { ReactNode, ReactElement } from 'react';
import './ConditionalWrapper.css'; // Import the CSS file

interface ConditionalWrapperProps {
  condition: boolean | undefined;
  children: ReactNode;
  boxAnimation?: string
}

const ConditionalWrapper = ({
  condition,
  children,
  boxAnimation = 'fadeInUp  one',
}: ConditionalWrapperProps): ReactElement | null => (condition ? (
  <div className={`animate ${boxAnimation}`}>
    {children as ReactElement}
  </div>
) : null);

ConditionalWrapper.defaultProps = {
  boxAnimation: 'fadeInUp one',
};

export default ConditionalWrapper;
