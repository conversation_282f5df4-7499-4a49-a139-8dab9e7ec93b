import React from 'react';
import { render, screen } from '@testing-library/react';
import ConditionalWrapper from './ConditionalWrapper';

describe('ConditionalWrapper Component', () => {
  test('renders children when condition is true', () => {
    render(
      <ConditionalWrapper condition>
        <div data-testid="wrapped-content">Test Content</div>
      </ConditionalWrapper>,
    );

    // ✅ Ensure content is rendered
    expect(screen.getByTestId('wrapped-content')).toBeInTheDocument();

    // ✅ Ensure animation class is applied
    expect(screen.getByTestId('wrapped-content').parentElement).toHaveClass('animate fadeInUp one');
  });

  test('does not render children when condition is false', () => {
    render(
      <ConditionalWrapper condition={false}>
        <div data-testid="wrapped-content">Test Content</div>
      </ConditionalWrapper>,
    );

    // ✅ Ensure content is NOT rendered
    expect(screen.queryByTestId('wrapped-content')).not.toBeInTheDocument();
  });

  test('applies the correct animation class when boxAnimation is provided', () => {
    render(
      <ConditionalWrapper condition boxAnimation="fadeOutDown">
        <div data-testid="wrapped-content">Test Content</div>
      </ConditionalWrapper>,
    );

    // ✅ Ensure custom animation class is applied
    expect(screen.getByTestId('wrapped-content').parentElement).toHaveClass('animate fadeOutDown');
  });
});
