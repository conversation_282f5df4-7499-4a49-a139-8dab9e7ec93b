import React from 'react';
import { render, screen } from '@testing-library/react';
import ConditionalLoaderWrapper from './ConditionalLoaderWrapper';

// ✅ Mock Loader component
jest.mock('shared/Loader', () => () => <div data-testid="loader">Loading...</div>);

describe('ConditionalLoaderWrapper Component', () => {
  test('renders Loader when isLoading is true', () => {
    render(
      <ConditionalLoaderWrapper isLoading>
        <div>Test Content</div>
      </ConditionalLoaderWrapper>,
    );

    // ✅ Check that Loader is displayed
    expect(screen.getByTestId('loader')).toBeInTheDocument();
    expect(screen.queryByTestId('content')).not.toBeInTheDocument(); // ✅ Ensure content is hidden
  });

  test('renders children when isLoading is false', () => {
    render(
      <ConditionalLoaderWrapper isLoading={false}>
        <div data-testid="child-content">Test Content</div>
      </ConditionalLoaderWrapper>,
    );

    // ✅ Ensure Loader is not displayed
    expect(screen.queryByTestId('loader')).not.toBeInTheDocument();

    // ✅ Ensure children are rendered
    expect(screen.getByTestId('child-content')).toBeInTheDocument();
  });
});
