import React from 'react';
import { Box } from '@mui/material';
import Loader from 'shared/Loader';

interface ConditionalLoaderWrapperProps {
  isLoading: boolean;
  children: any;
}

const ConditionalLoaderWrapper: React.FC<ConditionalLoaderWrapperProps> = ({ isLoading, children }) => (isLoading ? (
  <Box
    sx={{
 width: '100%', display: 'flex', justifyContent: 'center', marginTop: '10px',
}}
    data-testid="loader-container"
  >
    <Loader data-testid="loader" />
  </Box>
  ) : (
    <div data-testid="content">{children}</div>
  ));

export default ConditionalLoaderWrapper;
