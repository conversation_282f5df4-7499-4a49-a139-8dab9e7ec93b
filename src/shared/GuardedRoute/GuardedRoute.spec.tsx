import React from 'react';
import testRender from 'core/utilities/testUtils';
import GuardedRoute from './GuardedRoute';

describe('GuardedRoute', () => {
  it('should render children when isUserRoleClient is false', () => {
    const { getByText } = testRender(
      <GuardedRoute isUserRoleClient={false}>
        <div>Content</div>
      </GuardedRoute>,
    );

    expect(getByText('Content')).toBeInTheDocument();
  });

  it('should not render children when isUserRoleClient is true', () => {
    const { queryByText } = testRender(
      <GuardedRoute isUserRoleClient>
        <div>Content</div>
      </GuardedRoute>,
    );

    expect(queryByText('Content')).not.toBeInTheDocument();
  });
});
