import React from 'react';
import { Navigate } from 'react-router-dom';
import routes from 'SimAutomationRoute/routes';

interface IGuardedRouteProps {
  children: JSX.Element;
  isUserRoleClient?: boolean;
}

const GuardedRoute = ({ isUserRoleClient, children }: IGuardedRouteProps) => (
  isUserRoleClient ? <Navigate to={routes.simAutomateNotFound.path} /> : children
);

GuardedRoute.defaultProps = {
  isUserRoleClient: false,
};

export default GuardedRoute;
