import React from 'react';

import testRender from 'core/utilities/testUtils';
import SimManagementPlaceholder from './SimManagementPlaceholder';

const localRender = (title: string, state) => (
  testRender(<SimManagementPlaceholder
    title={title}
    loading={state}
  />)
);

describe('SimManagementPlaceholder', () => {
  test('should be rendered', () => {
    const { getByTestId } = localRender('title test', true);
    const placeholder = getByTestId('sim-management-placeholder');

    expect(placeholder).toBeInTheDocument();
  });
  test('should render proper loader', () => {
    const { getByTestId } = localRender('title test', true);
    const placeholderHeader = getByTestId('sim-management-loader');

    expect(placeholderHeader).toBeInTheDocument();
  });
  test('should render proper title', () => {
    const { getByTestId } = localRender('title test', false);
    const placeholderHeader = getByTestId('sim-management-placeholder__title');

    expect(placeholderHeader).toHaveTextContent('title test');
  });
});
