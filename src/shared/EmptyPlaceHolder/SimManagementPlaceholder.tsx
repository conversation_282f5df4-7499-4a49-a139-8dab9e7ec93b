import React, { ReactNode } from 'react';
import { AiOutlineFileText } from 'react-icons/ai';
import { Box, useTheme } from '@mui/material';

import Loader from 'shared/Loader';

import './SimManagementPlaceholder.scss';
import { useAppContext } from 'AppContextProvider';

interface ISimManagementPlaceholderProps {
  title: string,
  loading: boolean,
  icon?: ReactNode,
  text?: string
}

const SimManagementPlaceholder = ({
  loading, title, icon, text,
}:
  ISimManagementPlaceholderProps) => {
  const theme = useTheme();
  const { currentTheme } = useAppContext()

  return (
    <Box className="sim-management-placeholder" data-testid="sim-management-placeholder">
      {loading && (
        <Box display="flex" alignItems="center" data-testid="sim-management-loader">
          <Loader staticColor="#f5f1fa" size={60} />
        </Box>

      )}
      {(!loading && title) && (
        <Box display="flex" flexDirection="column" alignItems="center">
          <Box className="sim-management-placeholder__icon" sx={{
            background: currentTheme?.actionBg
          }}>
            {icon || <AiOutlineFileText size={35} color={theme.palette.primary.main} />}
          </Box>
          <h3
            className="sim-management-placeholder__title"
            data-testid="sim-management-placeholder__title"
          >
            {title}
          </h3>
          {text && (
            <p
              className="sim-management-placeholder__text"
              data-testid="sim-management-placeholder__text"
            >
              {text}
            </p>
          )}
        </Box>
      )}

    </Box>

  );
};

SimManagementPlaceholder.defaultProps = {
  icon: null,
  text: null,
};
export default SimManagementPlaceholder;
