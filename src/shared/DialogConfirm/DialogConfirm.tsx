import { Box, Button, Typography } from '@mui/material';
import React from 'react';
import Dialog from 'shared/Dialog/Dialog';

interface IDialogConfirm {
  open: boolean,
  onSuccess: () => void,
  onClose: () => void,
  status: string,
  simsCount: any
}

const DialogConfirm = ({
  open, onSuccess, onClose, status, simsCount,
}: IDialogConfirm) => {
  const displayActiveIcon = status === 'Deactivated' || status === 'Ready for Activation';
  const displayDeactiveIcon = status === 'Active';
  let statusMessage;
  if (displayActiveIcon) {
    statusMessage = 'Active';
  } else if (displayDeactiveIcon) {
    statusMessage = 'Deactivated';
  }

  return (

    <Dialog
      title="Are you sure?"
      onClose={() => onClose()}
      open={open}
      sx={{
        '& .MuiDialogTitle-root': {
          p: 8,
        },

      }}
      footerchildren={(
        <Box
          display="flex"
          alignItems="flex-start"
          gap="15px"
          justifyContent="space-between"
        >
          <Button
            sx={{ p: '0px 25px' }}
            variant="contained"
            color="primary"
            onClick={() => onSuccess()}
          >
            Confirm

          </Button>

          <Button
            sx={{ p: '0px 25px', backgroundColor: '#ebe3f6', border: '0px' }}
            variant="outlined"
            onClick={() => onClose()}
          >
            Cancel
          </Button>

        </Box>
      )}
    >

      <Typography
        variant="body1"
        component="p"
        sx={{ paddingBottom: '32px' }}
      >
        {(simsCount && simsCount !== '' && simsCount > 1) ? (
          <Box display="flex">
            After confirmation, selected
            <Box component="span" sx={{ fontWeight: 'bold', marginLeft: '5px', marginRight: '5px' }}>{simsCount}</Box>
            SIM statuses will be changed to
            {' '}
            {statusMessage}
          </Box>
        ) : (
          <div>
            After confirmation, selected  SIM status will be changed to
            {' '}
            {statusMessage}
          </div>
        )}
      </Typography>

    </Dialog>

  );
};

export default DialogConfirm;
