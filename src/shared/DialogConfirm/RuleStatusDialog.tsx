import { Box, Button, Typography } from '@mui/material';
import { useAppContext } from 'AppContextProvider';
import React from 'react';
import Dialog from 'shared/Dialog/Dialog';

interface IRatePlanDialog {
  open: boolean,
  onSuccess: () => void,
  onClose: () => void,
  ruleName: string,
  ruleStatus: boolean
}

const RuleStatusDialog = ({
  open, onSuccess, onClose, ruleName, ruleStatus,
}: IRatePlanDialog) => {
  const { currentTheme } = useAppContext();
  return (
    <Dialog
      title="Are you sure?"
      onClose={() => onClose()}
      open={open}
      sx={{
        '& .MuiDialogTitle-root': {
          p: 8,
        },

      }}
      footerchildren={(
        <Box
          display="flex"
          alignItems="flex-start"
          gap="15px"
          justifyContent="space-between"
        >
          <Button
            sx={{ p: '0px 25px' }}
            variant="contained"
            color="primary"
            onClick={() => onSuccess()}
          >
            Confirm

          </Button>

          <Button
            sx={{ p: '0px 25px', backgroundColor: currentTheme?.actionBg || '#ebe3f6', border: '0px' }}
            variant="outlined"
            onClick={() => onClose()}
          >
            Cancel
          </Button>

        </Box>
      )}
    >
      <Typography
        variant="body1"
        component="p"
        sx={{ paddingBottom: '32px' }}
      >
        <p>
          Are you sure you want to
          {' '}
          {ruleStatus ? 'deactivate' : 'activate'}
          {' '}
          <strong>{ruleName}</strong>
          {' '}
          rule?
          {ruleStatus && (
            <>
              <br />
              <span> All its processes will be stopped.</span>
            </>
          )}
        </p>
      </Typography>
    </Dialog>
  )
};

export default RuleStatusDialog;
