import { TimelineDot } from '@mui/lab';
import {
  Box, Typography, useTheme,
} from '@mui/material';

import React from 'react';

interface IStatusText {
     message: string | number
    status: 'error' | 'warning' | 'active' |'unknown' | 'deactivated' | 'pending' | 'total' | 'ready for activation' |'totalothers' | 'totalactive';
  }

export default function StatusText({ status = 'unknown', message }:IStatusText) {
  const statusLabel = status.toLocaleLowerCase();
  const theme = useTheme();
  const bold = {
    color: 'rgba(0, 0, 0, 0.87)',
    fontFamily: '"Open Sans",sans-serif',
    fontWeight: 700,
    fontSize: '14px',
    lineHeight: 1.43,
    whiteSpace: 'nowrap',
  };
  return (
    <Box
      sx={{
        '& .MuiTimelineDot-filled': {
          margin: '8px 0px',
          boxShadow: 'unset',
          padding: '2px',
        },
        marginLeft: '8px',
      }}
      display="flex"
      flexDirection="row"
      alignItems="center"
      gap={2}
    >
      <TimelineDot
        sx={{
          ...((statusLabel === 'active' || statusLabel === 'totalactive') && {
            backgroundColor: theme?.palette?.success.light,
          }),
          ...((statusLabel === 'error' || statusLabel === 'deactivated') && {
            backgroundColor: '#F7735D',
          }),
          ...((statusLabel === 'error' || statusLabel === 'totalothers') && {
            backgroundColor: '#AA8ADA',
          }),
          ...((statusLabel === 'error' || statusLabel === 'total') && {
            backgroundColor: '#AA8ADA',
          }),
          ...((statusLabel === 'ready for activation') && {
            backgroundColor: '#C2C2C2',
          }),
          ...((statusLabel === 'unknown'
           || statusLabel === 'pending') && {
            backgroundColor: '#FFE426',
          }),

        }}
      />
      {(statusLabel === 'totalothers' || statusLabel === 'totalactive') && (
      <Typography variant="h3" sx={bold}>{message}</Typography>)}

      {!(statusLabel === 'totalothers' || statusLabel === 'totalactive') && (
      <Typography variant="body2" sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>{message}</Typography>)}
    </Box>
  );
}
