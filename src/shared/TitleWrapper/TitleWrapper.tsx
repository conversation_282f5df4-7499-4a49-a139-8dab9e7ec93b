import { Box } from '@mui/material';
import React from 'react';

interface TitleWrapperProps {
    title: string;
    className?: string;
}

function TitleWrapper({ title, className = '' }: TitleWrapperProps) {
    return (
      <Box>
        <Box
          sx={{
                fontWeight: '700',
                color: '#333',
                padding: '12px 15px',
                borderRadius: '4px',
                fontSize: '13px',
                backgroundColor: '#F5F5F9',
                lineHeight: '1.231',
            }}
          className={className}
        >
          {title}
        </Box>
      </Box>
    );
}

TitleWrapper.defaultProps = {
    className: '',
};

export default TitleWrapper;
