import React from 'react';

import testRender from 'core/utilities/testUtils';
import Image from './Image';

const defaultMockProps = {
  alt: 'Tesla',
  src: 'http://logo-url.com/',
  height: '100px',
  width: '100px',
  style: {},
};

describe('describe Image exisit', () => {
  test('should have title', async () => {
    const mockProps = {
      ...defaultMockProps,
    };
    const { getByTestId } = testRender(<Image {...mockProps} />);

    const ratePlansListPlaceholder = getByTestId('image');

    expect(ratePlansListPlaceholder).toBeInTheDocument();
  });
});
