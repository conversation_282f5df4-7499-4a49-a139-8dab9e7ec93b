import Button from '@mui/material/Button';
import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import ArrowBack from 'assets/images/arrow-back.svg';
import Loader from 'shared/Loader';
import CommonAuthwrapper from 'core/CommonAuthwrapper';
import './TopBar.scss';

interface ITopBarProps {
  buttonAction?: () => void;
  buttonDisabled?: boolean;
  buttonIcon?: React.ReactElement;
  buttonLoading?: boolean;
  buttonText?: string;
  children?: React.ReactNode;
  className?: string;
  navigateTo?: any;
   
  type?: string | any;
  repositories?: string[];
  permissions?: string[];
}

const TopBar = ({
  buttonAction,
  buttonDisabled,
  buttonIcon,
  buttonLoading,
  buttonText,
  children,
  className,
  type,
  navigateTo = -1,
  repositories = [],
  permissions = [],
  ...props
}: ITopBarProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const isEdit = location.pathname.includes('view');
  // const { isUserRoleClient } = useContext(RouteContext);
  const navigateBack = () => {
    const baseUrl = window.location.origin;

    if (navigateTo === baseUrl) {
      window.location.href = navigateTo;
    } else {
      navigate(navigateTo);
    }
  };

  return (
    <div {...props} className={`automation-top-bar ${className}`}>
      <div className="automation-top-bar__items">
        <button onClick={navigateBack} type="button" className="automation-top-bar__items_back-button">
          <img src={String(ArrowBack)} alt="arrow back" />
        </button>
        {
          children
        }
      </div>
      {!isEdit && (
        <CommonAuthwrapper
          permission={permissions}
          repository={repositories}
        >
          <Button
            className="automation-top-bar__button"
            data-testid="automation-top-bar__button"
            variant="contained"
            size="large"
            color="primary"
            type={type}
            onClick={buttonAction}
            disabled={!!buttonDisabled}
          >
            {
              buttonLoading
                ? <Loader />
                : (
                  <>
                    {buttonIcon}
                    {buttonText}
                  </>
                )
            }
          </Button>
        </CommonAuthwrapper>
      )}
    </div>
  );
};

TopBar.defaultProps = {
  buttonDisabled: false,
  buttonIcon: null,
  buttonLoading: false,
  children: '',
  className: '',
  type: 'button',
  navigateTo: -1,
  buttonText: '',
  buttonAction: null,
  permissions: [],
  repositories: [],
};

export default TopBar;
