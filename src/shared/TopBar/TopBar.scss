.automation-top-bar {
  display: flex;
  justify-content: space-between !important;

  &__button {
    height: 40px;
    text-transform: capitalize !important;

    &.MuiButton-root {
      // font-family: 'BT Curve, sans-serif';
      font-size: 14px;
    }

    .anticon {
      margin-right: 7px;
      font-size: 17px;
    }
  }

  &__items {
    display: flex;
    align-items: center;

    &_back-button {
      width: 40px;
      height: 40px;
      background: none;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease-out 100ms;
      margin-right: 10px;

      &:hover {
        background: var(--light-background, #f5f1fa);
      }
    }

    a {
      cursor: pointer;
      margin-right: 10px;
    }

    img {
      cursor: pointer;
    }
  }
}

@media screen and (max-width: 1600px) {
  .automation-top-bar {
    &__button.MuiButton-root {
      font-size: 13px;
    }
  }
}
