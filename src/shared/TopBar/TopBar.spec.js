import React from 'react';
import { fireEvent } from '@testing-library/react';
import { useNavigate } from 'react-router-dom';
import testRender from 'core/utilities/testUtils';
import TopBar from './TopBar';

const defaultMockProps = {
  className: '',
  children: '',
  type: 'button',
  buttonText: 'submit',
  buttonAction: jest.fn(),
};
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'), // Use the actual implementation for other functions
  useNavigate: jest.fn(), // Mock the specific function you are using
}));

describe('TopBar', () => {
  test('should render "submit" text in button', () => {
    const mockProps = {
      ...defaultMockProps,
    };

    const mockNavigate = jest.fn();
    useNavigate.mockImplementation(() => mockNavigate);

    const { getByTestId } = testRender(<TopBar {...mockProps} />);
    const submitButton = getByTestId('automation-top-bar__button');

    expect(submitButton).toHaveTextContent('submit');
  });

  test('should render loader', () => {
    const mockProps = {
      ...defaultMockProps,
      buttonLoading: true,
    };
    const mockNavigate = jest.fn();
    useNavigate.mockImplementation(() => mockNavigate);
    const { getByTestId } = testRender(<TopBar {...mockProps} />);
    const loader = getByTestId('loader');

    expect(loader).toBeInTheDocument();
  });

  test('should call "buttonAction" if user click "submit" button ', () => {
    const mockProps = {
      ...defaultMockProps,
    };
    const mockNavigate = jest.fn();
    useNavigate.mockImplementation(() => mockNavigate);
    const { queryByTestId } = testRender(<TopBar {...mockProps} />);
    const submitButton = queryByTestId('automation-top-bar__button');

    fireEvent.click(submitButton);

    expect(mockProps.buttonAction).toBeCalled();
  });
});
