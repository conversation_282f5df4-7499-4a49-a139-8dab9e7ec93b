import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import CurrencyInput from './CurrencyInput';

describe('CurrencyInput', () => {
  test('should render correct', () => {
    const { getByTestId } = render(<CurrencyInput />);
    const input = getByTestId('currency-input');

    expect(input).toBeInTheDocument();
  });

  test('should input value be without decimals', () => {
    const { getByTestId } = render(<CurrencyInput />);

    const input = getByTestId('currency-input');

    fireEvent.change(input, { target: { value: '13' } });

    expect(input.getAttribute('value')).toEqual('13');
  });
});
