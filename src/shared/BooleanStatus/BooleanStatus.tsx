import { TimelineDot } from '@mui/lab';
import { Box, Typography, useTheme } from '@mui/material';
import React from 'react';

interface IBooleanStatus {
  status: boolean;
}

export default function BooleanStatus({ status }: IBooleanStatus) {
  const theme = useTheme();
  const bold = {
    color: 'rgba(0, 0, 0, 0.87)',
    fontFamily: '"Open Sans",sans-serif',
    fontWeight: 700,
    fontSize: '14px',
    lineHeight: 1.43,
    whiteSpace: 'nowrap',
  };

  return (
    <Box
      sx={{
        '& .MuiTimelineDot-filled': {
          margin: '8px 0px',
          boxShadow: 'unset',
          padding: '2px',
        },
      }}
      display="flex"
      flexDirection="row"
      alignItems="center"
      minWidth="75px"
      gap={2}
    >
      <TimelineDot
        sx={{
          backgroundColor: status ? theme?.palette?.success.light : '#F7735D',
        }}
      />
      <Typography variant="h3" sx={bold}>
        {status ? 'Active' : 'Inactive'}
      </Typography>
    </Box>
  );
}
