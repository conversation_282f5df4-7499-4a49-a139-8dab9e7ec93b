import { render, screen } from '@testing-library/react';
import { useTheme } from '@mui/material/styles'; // Import the hook
import React from 'react';
import BooleanStatus from './BooleanStatus';

// Mock the useTheme hook to return a custom theme
jest.mock('@mui/material/styles', () => ({
  ...jest.requireActual('@mui/material/styles'),
  useTheme: jest.fn(),
}));

describe('BooleanStatus Component', () => {
  it('should render the BooleanStatus component with Active text when status is true', () => {
    // Mock the useTheme hook
    useTheme.mockReturnValue({
      palette: {
        success: {
          main: '#34C759',
          light: '#81c784', // Light success color
        },
      },
    });

    render(<BooleanStatus status />);
    expect(screen.getByText('Active')).toBeInTheDocument();
  });

  it('should render the BooleanStatus component with Inactive text when status is false', () => {
    // Mock the useTheme hook
    useTheme.mockReturnValue({
      palette: {
        success: {
          main: '#34C759',
          light: '#81c784',
        },
      },
    });

    render(<BooleanStatus status={false} />);
    expect(screen.getByText('Inactive')).toBeInTheDocument();
  });

  it('should have the correct background color for the TimelineDot when status is false', () => {
    // Mock the useTheme hook
    useTheme.mockReturnValue({
      palette: {
        success: {
          main: '#34C759',
          light: '#81c784',
        },
      },
    });

    render(<BooleanStatus status />);
    const text = screen.getByText('Active');
    expect(text).toHaveStyle({
      color: 'rgba(0, 0, 0, 0.87)',
      fontFamily: '"Open Sans",sans-serif',
      fontWeight: '700',
      fontSize: '14px',
      lineHeight: '1.43',
      whiteSpace: 'nowrap',
    });
  });
});
