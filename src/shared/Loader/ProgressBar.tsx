import { Box, CircularProgress } from '@mui/material';
import React from 'react';

export default function ProgressBar() {
  return (
    <Box
      style={{
        position: 'absolute',
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)',
      }}
      component="td"
    >
      <CircularProgress
        variant="indeterminate"
        disableShrink
        sx={{
          color: (theme) => theme.palette.secondary.main,
          animationDuration: '550ms',

        }}
        size={60}
        thickness={4}
      />
    </Box>
  );
}
