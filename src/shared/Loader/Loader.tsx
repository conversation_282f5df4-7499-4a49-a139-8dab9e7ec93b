import React from 'react';
import PropTypes from 'prop-types';
import { Box, CircularProgress } from '@mui/material';

interface ILoaderProps {
  size: number;
  staticColor: string;
}

const Loader = ({ size, staticColor, ...props }: ILoaderProps) => (
  <Box sx={{ position: 'relative', margin: 'auto' }}>
    <CircularProgress
      data-testid="loader"
      variant="determinate"
      sx={{
        color: staticColor,
      }}
      size={size}
      thickness={4}
      {...props}
      value={100}
    />
    <CircularProgress
      variant="indeterminate"
      disableShrink
      sx={{
        color: (theme) => theme.palette.secondary.main,
        animationDuration: '550ms',
        position: 'absolute',
        left: 0,
      }}
      size={size}
      thickness={4}
      {...props}
    />
  </Box>
);

Loader.propTypes = {
  size: PropTypes.number,
  staticColor: PropTypes.string,
};

Loader.defaultProps = {
  size: 25,
  staticColor: '#fff',
};

export default Loader;
