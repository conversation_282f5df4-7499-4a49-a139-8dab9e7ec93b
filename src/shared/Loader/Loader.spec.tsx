import React from 'react';
import { render } from '@testing-library/react';
import Loader from './Loader';

describe('Loader', () => {
  test('should render Loader', () => {
    const { getByTestId } = render(<Loader />);
    const loader = getByTestId('loader');

    expect(loader).toBeInTheDocument();
  });

  test('should render Loader with correct size', () => {
    const { container } = render(<Loader size={20} />);
    const progressbar = container.querySelector('[role="progressbar"]');

    expect(progressbar).toHaveStyle('width: 20px');
  });
});
