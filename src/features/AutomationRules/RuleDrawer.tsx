import React from 'react';
import { CloseOutlined } from '@ant-design/icons';
import { Box } from '@mui/material';
import './RuleDrawer.scss';
import { useAppContext } from 'AppContextProvider';

interface RuleDrawerProps {
  headerClass?: string;
  close: () => void;
  heading: string;
}

function RuleDrawer({ headerClass = '', close, heading }: RuleDrawerProps) {
    const { currentTheme } = useAppContext()
  
  return (
    <Box>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          background: currentTheme?.primaryBgGradient
        }}
        className={`drawer-header ${headerClass}`}
      >
        <h3 className="drawer-header-title">
          {heading}
        </h3>
        <CloseOutlined style={{ fontSize: '28px' }} onClick={() => { close(); }} />
      </Box>
    </Box>
  );
}

RuleDrawer.defaultProps = {
  headerClass: '',
};

export default RuleDrawer;
