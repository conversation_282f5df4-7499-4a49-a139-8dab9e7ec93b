import { coreAxios } from 'core/services/HTTPService';
import { IRatePlanCompany } from 'user.model';

export const getAccesss = () => coreAxios.get('/authorization/user/scope');

export const getAccounts = (
    page: number,
    pageSize: number,
    field?: string,
    ordering?: string,
    search?: string,
    signal?: AbortSignal,
  ) => {
    let url = `/glass/rule?page=${page}&page_size=${pageSize}`;

    if (ordering === 'asc') url += `&ordering=${field}`;
    if (ordering === 'desc') url += `&ordering=-${field}`;

    if (search) url += `&search=${search}`;
    if (signal) return coreAxios.get(url, { signal });
    return coreAxios.get(url);
  };

  export const getCategory = () => coreAxios.get('/glass/rule/category');

  export const getDefination = (id: string | number) => coreAxios.get(`/glass/rule/${id}/definition`);

  export const getRuleDetails = (id: string | number) => coreAxios.get(`/glass/rule/${id}/rule-rely`);

  export const getRuleData = (id: string) => coreAxios.get(`/glass/rule/${id}`);

  export const deleteRule = (id:string|number) => coreAxios.delete(`glass/rule?rules_uuid=${id}`);

  export const getUnits = () => coreAxios.get('/glass/rule/rule-unit');

  export const getActions = () => coreAxios.get('/glass/rule/rule-action');

  export const submitRule = (data: any) => coreAxios.post('/glass/rule', data);

  export const updateRuleData = (id: string, data:any) => coreAxios.put(`/glass/rule?rules_uuid=${id}`, data);

  export const ruleStatusbyId = (id: string, status: boolean) => coreAxios.patch(`/glass/rule/${id}/${status}`);

  export const lockStatusbyId = (id: string, status: boolean) => coreAxios.patch(`/glass/rule/${id}/lock/${status}`);

  export const getAccountId = (id: string) => coreAxios.get(`/glass/accounts/${id}/info`);

  export const getAccountDetails = (id: string) => coreAxios.get(`/glass/accounts/${id}`);

  export const getAllAccountName = () => coreAxios.get('/glass/account-names');

export const getRatePlansById = (id: number) => coreAxios.get<IRatePlanCompany>(`/glass/rate-plans/by-accounts/${id}`);
