.drawer-header {
  box-shadow: rgba(136, 91, 203, 0.15) 0px 6px 16px;
  color: white;
  transition: all 0.3s ease-in-out 25ms;
  padding: 20px 15px;
  .drawer-header-title {
    font-weight: 500;
    font-size: 18px;
    line-height: 22px;
    font-family: "Open Sans", sans-serif;
  }
}

.customAutoComplete {
  .MuiOutlinedInput-adornedEnd {
    padding: 2px !important;
    .MuiAutocomplete-endAdornment {
      top: 50% !important;
    }
    .MuiInputLabel-shrink {
      transform: "translate(14px, -8px) scale(0.7) !important";
    }
  }
}
.volumeClass {
  width: 50%;
}

.textField {
  margin-top: 24px !important;
  .MuiInputBase-root {
    height: 40px;
  }
  .MuiInputLabel-outlined {
    transform: translate(14px, 9px) scale(1);
  }
}
