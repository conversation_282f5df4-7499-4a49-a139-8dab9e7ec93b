// RuleDrawer.test.tsx
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import RuleDrawer from './RuleDrawer';

describe('RuleDrawer component', () => {
  const mockClose = jest.fn();
  const heading = 'Test Heading';

  beforeEach(() => {
    mockClose.mockClear();
  });

  test('renders the heading', () => {
    render(<RuleDrawer close={mockClose} heading={heading} />);
    expect(screen.getByText(heading)).toBeInTheDocument();
  });

  test('renders the close button', () => {
    render(<RuleDrawer close={mockClose} heading={heading} />);
    const closeButton = screen.getByRole('img', { hidden: true }); // Since CloseOutlined uses an icon which is treated as an image
    expect(closeButton).toBeInTheDocument();
  });

  test('calls the close function when the close button is clicked', () => {
    render(<RuleDrawer close={mockClose} heading={heading} />);
    const closeButton = screen.getByRole('img', { hidden: true }); // Since CloseOutlined uses an icon which is treated as an image
    fireEvent.click(closeButton);
    expect(mockClose).toHaveBeenCalledTimes(1);
  });

  test('applies the custom header class when provided', () => {
    const customClass = 'custom-header-class';
    render(<RuleDrawer close={mockClose} heading={heading} headerClass={customClass} />);
    const headerElement = screen.getByText(heading).parentElement;
    expect(headerElement).toHaveClass('drawer-header');
    expect(headerElement).toHaveClass(customClass);
  });

  test('uses the default header class when no custom class is provided', () => {
    render(<RuleDrawer close={mockClose} heading={heading} />);
    const headerElement = screen.getByText(heading).parentElement;
    expect(headerElement).toHaveClass('drawer-header');
  });
});
