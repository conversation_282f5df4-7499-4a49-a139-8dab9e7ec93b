// // EnhancedTableHead.test.tsx

import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import EnhancedTableHead from './EnhanchedTable';

type Order = 'asc' | 'desc';
interface TestData {
    RuleCategory: string;
    RuleName: string;
    RuleDefinition: string;
    dataVolume: string;
    Actions: string;
    NotificationConfig: string;
    Status: string;
    AccountName: string;
}
interface EnhancedTableProps {
    onRequestSort: (event: React.MouseEvent<unknown>, property: keyof TestData) => void;
    order: Order;
    orderBy: string;
    view?: boolean;
    isLoading: boolean;
}
const mockProps: EnhancedTableProps = {
    onRequestSort: jest.fn(),
    order: 'asc',
    orderBy: 'RuleCategory',
    view: false,
    isLoading: false,
};

test('renders table id correctly', () => {
    const { getByTestId } = render(<EnhancedTableHead {...mockProps} />);
    const ruleCategoryElement = getByTestId('table-head');
    expect(ruleCategoryElement).toBeInTheDocument();
});

test('renders table head correctly', () => {
    const { getByText } = render(<EnhancedTableHead {...mockProps} />);
    const ruleCategoryElement = getByText('Rule Category');
    expect(ruleCategoryElement).toBeInTheDocument();
});

test('clicking on TableSortLabel calls onRequestSort with correct arguments', () => {
    const { getByText } = render(<EnhancedTableHead {...mockProps} />);
    const tableSortLabel = getByText('Rule Category');

    fireEvent.click(tableSortLabel);
    // expect(mockProps.onRequestSort).toHaveBeenCalledWith(expect.any(Object), 'RuleCategory');
});
