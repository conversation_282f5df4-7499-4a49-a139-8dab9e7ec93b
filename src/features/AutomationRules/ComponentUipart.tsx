import {
    Box, styled, TableContainer,
  } from '@mui/material';

  const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
    minHeight: '60vh',
    // maxHeight: 'calc(100vh - 203px)',
    // minHeight: 'calc(100vh - 210px)',
    overflowX: 'auto',
    '& .MuiTable-root': {
      borderCollapse: 'separate',
      borderSpacing: '0 15px',
    },
    '& .MuiTableCell-body': {
      borderTop: '1px solid #E7E7EE',
      borderBottom: '1px solid #E7E7EE',
      padding: '12px 6px',
      // fontFamily: 'BTCurve, sans-serif',
      fontStyle: 'normal',
      fontWeight: 400,
      fontSize: '14px',
      lineHeight: '140%',
      color: '#333333',
      '&:first-of-type, &:last-of-type': {
        borderRadius: '4px',
      },

    },
    '.action-table-cell': {
      '& .MuiButtonBase-root': {
        '&:hover path': {
          fill: theme.palette.secondary.main,
        },
      },
    },
    '& .MuiTableBody-root': {
      '& .MuiTableCell-body': {
        ':first-of-type': {
          borderLeftWidth: '4px',
          borderLeftStyle: 'solid',
          borderLeftColor: theme.palette.grey,
        },
      },
    },
    '& .MuiTableBody-root .MuiTableRow-root:hover': {
      backgroundColor: 'unset',
      boxShadow: '0px 6px 16px rgba(136, 91, 203, 0.15)',
      '& .MuiTableCell-body': {
        borderTop: 'none',
        ':first-of-type': {
          borderLeftWidth: '4px',
          borderLeftStyle: 'solid',
          borderLeftColor: theme.palette.secondary.main,
        },
      },

      '& .MuiBox-root': {
        visibility: 'visible',
      },

    },
    '& .MuiTableCell-head': {
      // minWidth: '110px',
      paddingLeft: '0px',
      borderTopColor: 'unset',
      // fontFamily: 'BT Curve, sans-serif',
      border: 'unset',
      backgroundColor: '#fff !important',
      fontStyle: 'normal',
      fontWeight: 700,
      fontSize: '14px',
      lineHeight: '17px',
      color: '#525252',
      alignItems: 'center',
      '&:first-of-type, &:last-of-type': {
        borderLeft: 'none!important',
        borderRight: 'none!important',
      },
    },
  }));

  const StyledBoxName = styled(Box)({
    // fontFamily: 'BT Curve, sans-serif',
    fontStyle: 'normal',
    fontWeight: 700,
    fontSize: '16px',
    lineHeight: '25px',
    alignItems: 'center',
    color: '#333333',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    width: '160px',
  });

  export {
    StyledBoxName, StyledTableContainer,
  };
