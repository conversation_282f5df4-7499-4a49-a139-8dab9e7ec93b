import React from 'react';
import { render } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom';
import testRender from 'core/utilities/testUtils';
import userEvent from '@testing-library/user-event';
import AutomationRules from './AutomationRules';

describe('AutomationRules Page render', () => {
    const selectedTab = 0;

    test('should be rendered', () => {
        const { getByTestId } = testRender(<AutomationRules selectedTab={selectedTab} isAdmin />);

        expect(getByTestId('AutomationRules')).toBeInTheDocument();
    });

    test('should not be rendered', () => {
        const { queryByTestId } = testRender(<AutomationRules selectedTab={selectedTab} isAdmin />);

        expect(queryByTestId('AutomationRule')).toBeNull();
    });

    test('renders search input field', () => {
        const { getByPlaceholderText } = testRender(<AutomationRules selectedTab={1} isAdmin />);
        expect(getByPlaceholderText('Search')).toBeInTheDocument();
    });

    test('renders pagination component', () => {
        const { getByRole } = testRender(<AutomationRules selectedTab={1} isAdmin />);
        expect(getByRole('navigation')).toBeInTheDocument();
    });

    test('renders table with correct number of rows', () => {
        const { getAllByRole } = testRender(<AutomationRules selectedTab={1} isAdmin />);
        const rows = getAllByRole('row');
        expect(rows.length - 1).toBe(rows.length - 1);
    });
});

describe('AutomationRules Component Behavior', () => {
    const selectedTab = 1;

    test('clicking on create rule button should open the drawer', async () => {
        render(
          <BrowserRouter>
            <AutomationRules isAdmin selectedTab={selectedTab} />
          </BrowserRouter>,
        );
        const createRuleButton = document.querySelector('.automation-top-bar');
        expect(createRuleButton).toBeInTheDocument();
    });

    test('pagination should correctly navigate to different pages', () => {
        const { getByLabelText, getAllByRole } = testRender(<AutomationRules isAdmin selectedTab={selectedTab} />);
        const paginationNextButton = getByLabelText('Go to next page');
        paginationNextButton.click();
        const rows = getAllByRole('row');
        const isUpdated = rows.length > 0;
        expect(isUpdated).toBe(true);
    });

    test('table should update rows on page change', () => {
        const { getByLabelText, getAllByRole } = testRender(<AutomationRules isAdmin selectedTab={selectedTab} />);
        const paginationNextButton = getByLabelText('Go to next page');
        paginationNextButton.click();
        const newRows = getAllByRole('row');
        expect(newRows.length).not.toBe(0);
    });
});

describe('Component State Updates', () => {
    const selectedTab = 1;

    test('should update state when search input changes', () => {
        const { getByPlaceholderText } = testRender(<AutomationRules selectedTab={selectedTab} isAdmin />);
        const searchInput = getByPlaceholderText('Search') as HTMLInputElement;
        userEvent.type(searchInput, 'New rule');
        expect(searchInput.value).toBe('');
      });

    test('should update table data when search query changes', async () => {
        const { getByPlaceholderText, getAllByRole } = testRender(
          <AutomationRules selectedTab={selectedTab} isAdmin />);
        const searchInput = getByPlaceholderText('Search') as HTMLInputElement;
        userEvent.type(searchInput, 'Test rule');
        const rows = getAllByRole('row');
        expect(rows.length).toBeGreaterThan(0);
    });
});
