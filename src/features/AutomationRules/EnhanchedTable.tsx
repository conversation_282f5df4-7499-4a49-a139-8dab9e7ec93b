import React from 'react';
import {
  Box,
  TableCell, TableHead, TableRow, TableSortLabel,
} from '@mui/material';
import { visuallyHidden } from '@mui/utils';

type Order = 'asc' | 'desc';
interface Data {
  RuleCategory: string;
  AccountName?: string;
  RuleName: string;
  RuleDefinition: string;
  dataVolume: string;
  Actions: string;
  // NotificationConfig: string;
  Status: string;
}
export const sortFieldNames: Data = {
  RuleCategory: 'rule_category',
  AccountName: 'account_name',
  RuleName: 'rule_name',
  RuleDefinition: 'rule_definition',
  dataVolume: 'data_volume',
  Actions: 'action',
  // NotificationConfig: 'notification_config',
  Status: 'status',
};
interface HeadCell {
  disablePadding: boolean;
  id: keyof Data;
  label: string;
  numeric: boolean;
  sorting: boolean;
  width?: string;
}
const defaultHeadCells: readonly HeadCell[] = [
  {
    id: 'RuleCategory',
    numeric: false,
    disablePadding: true,
    label: 'Rule Category',
    sorting: true,
    width: '240px',
  },
  {
    id: 'RuleName',
    numeric: false,
    disablePadding: true,
    label: 'Rule Name',
    sorting: true,
    width: '185px',
  },
  {
    id: 'RuleDefinition',
    numeric: true,
    disablePadding: false,
    label: 'Rule Definition',
    sorting: true,
    width: '280px',
  },
  {
    id: 'dataVolume',
    numeric: true,
    disablePadding: false,
    label: 'Value',
    sorting: true,
    width: '120px',
  },
  {
    id: 'Actions',
    numeric: true,
    disablePadding: false,
    label: 'Actions',
    sorting: false,
    width: '100px',
  },
  // {
  //   id: 'NotificationConfig',
  //   numeric: true,
  //   disablePadding: false,
  //   label: 'Notification Configuration',
  //   sorting: false,
  //   width: '220px',
  // },
  {
    id: 'Status',
    numeric: true,
    disablePadding: false,
    label: 'Status',
    sorting: true,
    width: '170px',
  },
];

interface EnhancedTableProps {
  onRequestSort: (event: React.MouseEvent<unknown>, property: keyof Data) => void;
  order: Order;
  orderBy: string;
  view?: boolean;
  isLoading: boolean;
}

export default function EnhancedTableHead(props: EnhancedTableProps) {
  const {
    order, orderBy, onRequestSort, view, isLoading,
  } = props;
  const createSortHandler = (property: keyof Data) => (event: React.MouseEvent<unknown>) => {
    onRequestSort(event, property);
  };
  const headCells = view
  ? [
      {
        id: 'AccountName',
        numeric: false,
        disablePadding: false,
        label: 'Account Name',
        sorting: true,
        width: '170px',
      } as HeadCell,
      ...defaultHeadCells,
    ]
  : defaultHeadCells;

  const conditionalHeadCells = isLoading
  ? [
      ...headCells,
      {
        id: 'dataVolume', // Unique ID for dummy column
        numeric: false,
        disablePadding: false,
        label: '',
        sorting: false,
        width: view ? '130px' : '30px !important',
      },
    ]
  : headCells;

  const cssProperties: any = visuallyHidden;
  return (
    <TableHead data-testid="table-head">
      <TableRow sx={{ minWidth: '1863px' }}>
        {conditionalHeadCells.map((headCell, index) => (
          <TableCell
            key={headCell.id}
            padding={headCell.disablePadding ? 'none' : 'normal'}
            sortDirection={orderBy === headCell.id ? order : false}
            variant="head"
            align="left"
            sx={{
              backgroundColor: 'lightblue',
              width: '260px',
              minWidth: `${headCell.width}`,
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              paddingLeft: (index === 0) ? '25px !important' : 'auto',
              '& .MuiTableSortLabel-root': {
                marginLeft: (index === 0) ? '12px' : '0px',
              },
            }}
          >
            {headCell.sorting ? (
              <TableSortLabel
                active={orderBy === headCell.id}
                direction={orderBy === headCell.id ? order : 'asc'}
                onClick={createSortHandler(headCell.id)}
              >
                {headCell.label}
                {orderBy === headCell.id ? (
                  <Box component="span" sx={cssProperties}>
                    {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                  </Box>
                ) : null}
              </TableSortLabel>
            ) : (
              <Box>
                {headCell.label}
              </Box>
            )}

          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}

EnhancedTableHead.defaultProps = {
  view: false,
};
