import { render, screen } from '@testing-library/react';
import React from 'react';
import AccountTableRow from './AutomationTableRow'; // Adjust the import path as needed

const simAutomationList = [
  {
    uuid: '1',
    accountName: 'Testing account',
    lock: false,
    logoKey: 'test',
    logoUrl: 'test',
    ruleCategory: 'Usage Monitoring',
    ruleType: 'Usage Monitoring',
    ruleName: 'Cycle to Date Data Usage',
    ruleDefinition: 'Data usage exceeds a specified limit',
    dataVolume: 400000,
    unit: 'KB',
    status: true,
    action: [{ name: 'Deactivate SIM', action: 'REACTIVATE_SIM', actionValue: 'Reactivate Sim' }],
    notification: {
      email: ['<EMAIL>'],
      sms: [],
      push: [],
    },
  },
  {
    uuid: '2',
    accountName: 'test',
    logoKey: 'test',
    lock: false,
    logoUrl: 'test',
    ruleCategory: 'Security',
    ruleType: 'Security',
    ruleName: 'IMEI Change',
    ruleDefinition: 'Trigger when detecting an IMEI change in accounting start request',
    dataVolume: 1,
    unit: 'KB',
    status: false,
    action: [{ name: 'Deactivate SIM', action: 'REACTIVATE_SIM', actionValue: 'Reactivate Sim' }],
    notification: {
      email: ['<EMAIL>'],
      sms: [],
      push: [],
    },
  },
];

describe('AccountTableRow Component', () => {
  const onEditClickHandler = jest.fn();
  const onViewClickHandler = jest.fn();
  const setDialog = jest.fn();
  const setUuid = jest.fn();
  const setSelectedRuleStatus = jest.fn();
  const setRuleName = jest.fn();

  it('should render the row correctly', () => {
    render(
      <AccountTableRow
        row={simAutomationList[0]}
        onEditClickHandler={onEditClickHandler}
        onViewClickHandler={onViewClickHandler}
        setDialog={setDialog}
        setUuid={setUuid}
        setSelectedRuleStatus={setSelectedRuleStatus}
        setRuleName={setRuleName}
      />,
    );
    expect(screen.getByTestId('table-row')).toBeInTheDocument();
  });

  it('should hide or show elements based on permissions', () => {
    render(
      <AccountTableRow
        row={simAutomationList[0]}
        onEditClickHandler={onEditClickHandler}
        onViewClickHandler={onViewClickHandler}
        setDialog={setDialog}
        setUuid={setUuid}
        setSelectedRuleStatus={setSelectedRuleStatus}
        setRuleName={setRuleName}
      />,
    );
  });
});
