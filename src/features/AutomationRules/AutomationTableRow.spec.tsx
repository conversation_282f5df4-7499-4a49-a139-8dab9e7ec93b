import { render, screen, fireEvent } from '@testing-library/react';
import React from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import AccountTableRow from './AutomationTableRow';

// Mock the AppContextProvider
jest.mock('AppContextProvider', () => ({
  useAppContext: () => ({
    currentTheme: {
      primaryColor: '#5514B4',
      actionBg: '#ebe3f6',
    },
  }),
}));

// Mock CommonAuthwrapper
jest.mock('core/CommonAuthwrapper', () => {
  return function MockCommonAuthwrapper({ children }: { children: React.ReactNode }) {
    return <div data-testid="auth-wrapper">{children}</div>;
  };
});

// Mock Image component
jest.mock('shared/LazyLoad/Image', () => {
  return function MockImage({ src, alt }: { src: string; alt: string }) {
    return <img src={src} alt={alt} data-testid="lazy-image" />;
  };
});

// Mock Batch component
jest.mock('shared/Batch/Batch', () => {
  return function MockBatch({ label }: { label: string }) {
    return <span data-testid="batch">{label}</span>;
  };
});

// Mock BooleanStatus component
jest.mock('shared/BooleanStatus/BooleanStatus', () => {
  return function MockBooleanStatus({ status }: { status: boolean }) {
    return <span data-testid="boolean-status">{status ? 'Active' : 'Inactive'}</span>;
  };
});

const mockRow = {
  uuid: '1',
  accountName: 'Testing account',
  lock: false,
  logoKey: 'test',
  logoUrl: 'test',
  ruleCategory: 'Usage Monitoring',
  ruleType: 'Usage Monitoring',
  ruleName: 'Cycle to Date Data Usage',
  ruleDefinition: 'Data usage exceeds a specified limit',
  dataVolume: 400000,
  unit: 'KB',
  status: true,
  action: [{ name: 'Deactivate SIM', action: 'REACTIVATE_SIM', actionValue: 'Reactivate Sim' }],
  notification: {
    email: ['<EMAIL>'],
    sms: [],
    push: [],
  },
};

const defaultProps = {
  row: mockRow,
  onEditClickHandler: jest.fn(),
  onViewClickHandler: jest.fn(),
  onLockClickHandler: jest.fn(),
  onStatusChange: jest.fn(),
  view: true,
  setDialog: jest.fn(),
  setUuid: jest.fn(),
  setSelectedRuleStatus: jest.fn(),
  setRuleName: jest.fn(),
};

const theme = createTheme();

const renderWithTheme = (ui: React.ReactElement) =>
  render(<ThemeProvider theme={theme}>{ui}</ThemeProvider>);

describe('AccountTableRow', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders account table row with all data', () => {
    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow {...defaultProps} />
        </tbody>
      </table>
    );

    expect(screen.getByText('Testing account')).toBeInTheDocument();
    expect(screen.getByText('Cycle to Date Data Usage')).toBeInTheDocument();
    expect(screen.getByText('Usage Monitoring')).toBeInTheDocument();
    expect(screen.getByTestId('table-row')).toBeInTheDocument();
  });

  test('renders without view prop (non-admin view)', () => {
    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow {...defaultProps} view={false} />
        </tbody>
      </table>
    );

    expect(screen.queryByTestId('lazy-image')).not.toBeInTheDocument();
    expect(screen.getByText('Cycle to Date Data Usage')).toBeInTheDocument();
  });

  test('handles edit button click', () => {
    const mockEdit = jest.fn();
    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow {...defaultProps} onEditClickHandler={mockEdit} />
        </tbody>
      </table>
    );

    const editButton = screen.getByRole('button', { name: /edit/i });
    fireEvent.click(editButton);
    expect(mockEdit).toHaveBeenCalledWith('1');
  });

  test('handles view button click', () => {
    const mockView = jest.fn();
    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow {...defaultProps} onViewClickHandler={mockView} />
        </tbody>
      </table>
    );

    const viewButton = screen.getByRole('button', { name: /view/i });
    fireEvent.click(viewButton);
    expect(mockView).toHaveBeenCalledWith('1');
  });

  test('handles lock button click when unlocked', () => {
    const mockLock = jest.fn();
    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow
            {...defaultProps}
            onLockClickHandler={mockLock}
            row={{ ...mockRow, lock: false }}
          />
        </tbody>
      </table>
    );

    const lockButton = screen.getByRole('button', { name: /lock/i });
    fireEvent.click(lockButton);
    expect(mockLock).toHaveBeenCalledWith('1', false, 'Testing account');
  });

  test('handles unlock button click when locked', () => {
    const mockLock = jest.fn();
    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow
            {...defaultProps}
            onLockClickHandler={mockLock}
            row={{ ...mockRow, lock: true }}
          />
        </tbody>
      </table>
    );

    const unlockButton = screen.getByRole('button', { name: /unlock/i });
    fireEvent.click(unlockButton);
    expect(mockLock).toHaveBeenCalledWith('1', true, 'Testing account');
  });

  test('handles status switch toggle', () => {
    const mockSetDialog = jest.fn();
    const mockSetUuid = jest.fn();
    const mockSetSelectedRuleStatus = jest.fn();
    const mockSetRuleName = jest.fn();

    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow
            {...defaultProps}
            setDialog={mockSetDialog}
            setUuid={mockSetUuid}
            setSelectedRuleStatus={mockSetSelectedRuleStatus}
            setRuleName={mockSetRuleName}
          />
        </tbody>
      </table>
    );

    const statusSwitch = screen.getByRole('checkbox');
    fireEvent.click(statusSwitch);

    expect(mockSetUuid).toHaveBeenCalledWith('1');
    expect(mockSetSelectedRuleStatus).toHaveBeenCalledWith(true);
    expect(mockSetRuleName).toHaveBeenCalledWith('Cycle to Date Data Usage');
    expect(mockSetDialog).toHaveBeenCalledWith(true);
  });

  test('displays formatted data volume', () => {
    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow {...defaultProps} />
        </tbody>
      </table>
    );

    expect(screen.getByText('400,000 KB')).toBeInTheDocument();
  });

  test('displays email notifications', () => {
    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow {...defaultProps} />
        </tbody>
      </table>
    );

    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  test('displays actions correctly', () => {
    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow {...defaultProps} />
        </tbody>
      </table>
    );

    expect(screen.getByText('Reactivate Sim')).toBeInTheDocument();
  });

  test('renders with different rule category', () => {
    const modifiedRow = {
      ...mockRow,
      ruleCategory: 'Data Monitoring',
    };

    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow {...defaultProps} row={modifiedRow} />
        </tbody>
      </table>
    );

    expect(screen.getByText('Data Monitoring')).toBeInTheDocument();
  });

  test('renders with inactive status', () => {
    const modifiedRow = {
      ...mockRow,
      status: false,
    };

    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow {...defaultProps} row={modifiedRow} />
        </tbody>
      </table>
    );

    const statusSwitch = screen.getByRole('checkbox');
    expect(statusSwitch).not.toBeChecked();
  });

  test('handles multiple actions', () => {
    const modifiedRow = {
      ...mockRow,
      action: [
        { name: 'Deactivate SIM', action: 'DEACTIVATE_SIM', actionValue: 'Deactivate Sim' },
        { name: 'Send Email', action: 'SEND_EMAIL', actionValue: 'Send Email' }
      ],
    };

    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow {...defaultProps} row={modifiedRow} />
        </tbody>
      </table>
    );

    // Check that the actions are rendered (they appear as action counts, not text)
    expect(screen.getByTestId('table-row')).toBeInTheDocument();
  });

  test('handles empty notification arrays', () => {
    const modifiedRow = {
      ...mockRow,
      notification: {
        email: [],
        sms: [],
        push: [],
      },
    };

    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow {...defaultProps} row={modifiedRow} />
        </tbody>
      </table>
    );

    expect(screen.getByTestId('table-row')).toBeInTheDocument();
  });

  test('handles large data volume formatting', () => {
    const modifiedRow = {
      ...mockRow,
      dataVolume: 1000000,
      unit: 'MB',
    };

    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow {...defaultProps} row={modifiedRow} />
        </tbody>
      </table>
    );

    // Indian number formatting: 10,00,000 instead of 1,000,000
    expect(screen.getByText('10,00,000 MB')).toBeInTheDocument();
  });

  test('handles missing logo URL', () => {
    const modifiedRow = {
      ...mockRow,
      logoUrl: '',
    };

    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow {...defaultProps} />
        </tbody>
      </table>
    );

    expect(screen.getByTestId('table-row')).toBeInTheDocument();
  });

  test('handles different rule types', () => {
    const modifiedRow = {
      ...mockRow,
      ruleType: 'Data Limit Monitoring',
    };

    renderWithTheme(
      <table>
        <tbody>
          <AccountTableRow {...defaultProps} row={modifiedRow} />
        </tbody>
      </table>
    );

    expect(screen.getByText('Data Limit Monitoring')).toBeInTheDocument();
  });
});
