import { coreAxios } from 'core/services/HTTPService';
import {
  getAccesss,
  getAccounts,
  getCategory,
  getDefination,
  getRuleData,
  getUnits,
  getActions,
  submitRule,
  updateRuleData,
  ruleStatusbyId,
  getAccountId,
  getAccountDetails,
  getAllAccountName,
} from './api.service';

jest.mock('core/services/HTTPService');

describe('Service Functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call getAccesss with correct URL', async () => {
    (coreAxios.get as jest.Mock).mockResolvedValue({ data: 'mockedResponse' });

    const response = await getAccesss();

    expect(coreAxios.get).toHaveBeenCalledWith('/authorization/user/scope');
    expect(response.data).toBe('mockedResponse');
  });

  it('should call getAccounts with correct parameters', async () => {
    (coreAxios.get as jest.Mock).mockResolvedValue({ data: 'mockedResponse' });

    const response = await getAccounts(1, 10, 'name', 'asc', 'searchText');

    expect(coreAxios.get).toHaveBeenCalledWith(
      '/glass/rule?page=1&page_size=10&ordering=name&search=searchText',
    );
    expect(response.data).toBe('mockedResponse');
  });

  it('should call getAccounts with AbortSignal', async () => {
    const mockSignal = new AbortController().signal;
    (coreAxios.get as jest.Mock).mockResolvedValue({ data: 'mockedResponse' });

    const response = await getAccounts(1, 10, undefined, undefined, undefined, mockSignal);

    expect(coreAxios.get).toHaveBeenCalledWith('/glass/rule?page=1&page_size=10', { signal: mockSignal });
    expect(response.data).toBe('mockedResponse');
  });

  it('should call getCategory with correct URL', async () => {
    (coreAxios.get as jest.Mock).mockResolvedValue({ data: 'mockedResponse' });

    const response = await getCategory();

    expect(coreAxios.get).toHaveBeenCalledWith('/glass/rule/category');
    expect(response.data).toBe('mockedResponse');
  });

  it('should call getDefination with correct URL', async () => {
    const id = '123';
    (coreAxios.get as jest.Mock).mockResolvedValue({ data: 'mockedResponse' });

    const response = await getDefination(id);

    expect(coreAxios.get).toHaveBeenCalledWith(`/glass/rule/${id}/definition`);
    expect(response.data).toBe('mockedResponse');
  });

  it('should call getRuleData with correct URL', async () => {
    const id = '123';
    (coreAxios.get as jest.Mock).mockResolvedValue({ data: 'mockedResponse' });

    const response = await getRuleData(id);

    expect(coreAxios.get).toHaveBeenCalledWith(`/glass/rule/${id}`);
    expect(response.data).toBe('mockedResponse');
  });

  it('should call getUnits with correct URL', async () => {
    (coreAxios.get as jest.Mock).mockResolvedValue({ data: 'mockedResponse' });

    const response = await getUnits();

    expect(coreAxios.get).toHaveBeenCalledWith('/glass/rule/rule-unit');
    expect(response.data).toBe('mockedResponse');
  });

  it('should call getActions with correct URL', async () => {
    (coreAxios.get as jest.Mock).mockResolvedValue({ data: 'mockedResponse' });

    const response = await getActions();

    expect(coreAxios.get).toHaveBeenCalledWith('/glass/rule/rule-action');
    expect(response.data).toBe('mockedResponse');
  });

  it('should call submitRule with correct data', async () => {
    const mockData = { name: 'testRule' };
    (coreAxios.post as jest.Mock).mockResolvedValue({ data: 'mockedResponse' });

    const response = await submitRule(mockData);

    expect(coreAxios.post).toHaveBeenCalledWith('/glass/rule', mockData);
    expect(response.data).toBe('mockedResponse');
  });

  it('should call updateRuleData with correct URL and data', async () => {
    const id = '123';
    const mockData = { status: 'active' };
    (coreAxios.put as jest.Mock).mockResolvedValue({ data: 'mockedResponse' });

    const response = await updateRuleData(id, mockData);

    expect(coreAxios.put).toHaveBeenCalledWith(`/glass/rule?rules_uuid=${id}`, mockData);
    expect(response.data).toBe('mockedResponse');
  });

  it('should call ruleStatusbyId with correct URL and status', async () => {
    const id = '123';
    const status = true;
    (coreAxios.patch as jest.Mock).mockResolvedValue({ data: 'mockedResponse' });

    const response = await ruleStatusbyId(id, status);

    expect(coreAxios.patch).toHaveBeenCalledWith(`/glass/rule/${id}/${status}`);
    expect(response.data).toBe('mockedResponse');
  });

  it('should call getAccountId with correct URL', async () => {
    const id = '123';
    (coreAxios.get as jest.Mock).mockResolvedValue({ data: 'mockedResponse' });

    const response = await getAccountId(id);

    expect(coreAxios.get).toHaveBeenCalledWith(`/glass/accounts/${id}/info`);
    expect(response.data).toBe('mockedResponse');
  });

  it('should call getAccountDetails with correct URL', async () => {
    const id = '123';
    (coreAxios.get as jest.Mock).mockResolvedValue({ data: 'mockedResponse' });

    const response = await getAccountDetails(id);

    expect(coreAxios.get).toHaveBeenCalledWith(`/glass/accounts/${id}`);
    expect(response.data).toBe('mockedResponse');
  });

  it('should call getAllAccountName with correct URL', async () => {
    (coreAxios.get as jest.Mock).mockResolvedValue({ data: 'mockedResponse' });

    const response = await getAllAccountName();

    expect(coreAxios.get).toHaveBeenCalledWith('/glass/account-names');
    expect(response.data).toBe('mockedResponse');
  });
});
