import validateCheckRules from './ruleValidation';

describe('validateCheckRules', () => {
  let mockWatch: jest.Mock;
  let mockSetError: jest.Mock;
  let mockClearErrors: jest.Mock;

  beforeEach(() => {
    mockWatch = jest.fn();
    mockSetError = jest.fn();
    mockClearErrors = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return false and set error when rule name exceeds 50 characters', () => {
    const longRuleName = 'a'.repeat(51);
    mockWatch.mockReturnValue([false, false, false, longRuleName]);

    const result = validateCheckRules({
      watch: mockWatch,
      setError: mockSetError,
      clearErrors: mockClearErrors,
    });

    expect(result).toBe(false);
    expect(mockSetError).toHaveBeenCalledWith('ruleName', {
      type: 'manual',
      message: 'Rule name should be less than 50 characters',
    });
  });

  it('should clear rule name errors when rule name is valid', () => {
    mockWatch.mockReturnValue([true, false, false, 'Valid Rule Name']);

    const result = validateCheckRules({
      watch: mockWatch,
      setError: mockSetError,
      clearErrors: mockClearErrors,
    });

    expect(result).toBe(true);
    expect(mockClearErrors).toHaveBeenCalledWith('ruleName');
  });

  it('should return false and set error when rate plan change is required but not selected', () => {
    mockWatch.mockReturnValue([false, false, false, 'Valid Rule Name']);
    const ruleDetails = { action: { requiredRatePlanChange: true } };

    const result = validateCheckRules({
      watch: mockWatch,
      setError: mockSetError,
      clearErrors: mockClearErrors,
      ruleDetails,
    });

    expect(result).toBe(false);
    expect(mockSetError).toHaveBeenCalledWith('changeRatePlan', {
      type: 'manual',
      message: 'Rate plan action must be selected for this rule.',
    });
  });

  it('should clear rate plan errors when rate plan change is not required', () => {
    mockWatch.mockReturnValue([true, false, false, 'Valid Rule Name']);
    const ruleDetails = { action: { requiredRatePlanChange: false } };

    const result = validateCheckRules({
      watch: mockWatch,
      setError: mockSetError,
      clearErrors: mockClearErrors,
      ruleDetails,
    });

    expect(result).toBe(true);
    expect(mockClearErrors).toHaveBeenCalledWith('changeRatePlan');
  });

  it('should return false and set errors when no rules are selected', () => {
    mockWatch.mockReturnValue([false, false, false, 'Valid Rule Name']);

    const result = validateCheckRules({
      watch: mockWatch,
      setError: mockSetError,
      clearErrors: mockClearErrors,
    });

    expect(result).toBe(false);
    expect(mockSetError).toHaveBeenCalledWith('deActivateSim', {
      type: 'manual',
      message: 'Either deActivateSim or sendEmail must be selected',
    });
    expect(mockSetError).toHaveBeenCalledWith('emailStatus', {
      type: 'manual',
      message: 'Either deActivateSim or emailStatus must be selected',
    });
  });

  it('should clear rule errors when at least one rule is selected', () => {
    mockWatch.mockReturnValue([true, false, false, 'Valid Rule Name']);

    const result = validateCheckRules({
      watch: mockWatch,
      setError: mockSetError,
      clearErrors: mockClearErrors,
    });

    expect(result).toBe(true);
    expect(mockClearErrors).toHaveBeenCalledWith('deActivateSim');
    expect(mockClearErrors).toHaveBeenCalledWith('emailStatus');
  });

  it('should return true when emailStatus is selected', () => {
    mockWatch.mockReturnValue([false, true, false, 'Valid Rule Name']);

    const result = validateCheckRules({
      watch: mockWatch,
      setError: mockSetError,
      clearErrors: mockClearErrors,
    });

    expect(result).toBe(true);
  });

  it('should return true when changeRatePlan is selected', () => {
    mockWatch.mockReturnValue([false, false, true, 'Valid Rule Name']);

    const result = validateCheckRules({
      watch: mockWatch,
      setError: mockSetError,
      clearErrors: mockClearErrors,
    });

    expect(result).toBe(true);
  });

  it('should handle undefined ruleDetails', () => {
    mockWatch.mockReturnValue([true, false, false, 'Valid Rule Name']);

    const result = validateCheckRules({
      watch: mockWatch,
      setError: mockSetError,
      clearErrors: mockClearErrors,
      ruleDetails: undefined,
    });

    expect(result).toBe(true);
  });

  it('should handle ruleDetails without action property', () => {
    mockWatch.mockReturnValue([true, false, false, 'Valid Rule Name']);
    const ruleDetails = {};

    const result = validateCheckRules({
      watch: mockWatch,
      setError: mockSetError,
      clearErrors: mockClearErrors,
      ruleDetails,
    });

    expect(result).toBe(true);
  });
});
