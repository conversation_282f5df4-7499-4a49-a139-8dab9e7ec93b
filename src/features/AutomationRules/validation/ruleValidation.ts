import { UseFormWatch, UseFormSetError, UseFormClearErrors } from 'react-hook-form';
import { RuleFormValues } from '../RuleFormValues';

interface ValidateCheckRulesProps {
  watch: UseFormWatch<RuleFormValues>;
  setError: UseFormSetError<RuleFormValues>;
  clearErrors: UseFormClearErrors<RuleFormValues>;
  ruleDetails?: { action?: { requiredRatePlanChange?: boolean } };
}

const validateCheckRules = ({
 watch, setError, clearErrors, ruleDetails,
}: ValidateCheckRulesProps): boolean => {
  const values = watch(['deActivateSim', 'emailStatus', 'changeRatePlan', 'ruleName']);
  const checkRules = values[0] || values[1] || values[2];
  const checkRatePlan = ruleDetails?.action?.requiredRatePlanChange;
  const checkRuleName = values[3];

  if (checkRuleName?.length > 50) {
    setError('ruleName', {
      type: 'manual',
      message: 'Rule name should be less than 50 characters',
    });
    return false;
  }
  clearErrors('ruleName');

  if (checkRatePlan && !values[2]) {
    setError('changeRatePlan', {
      type: 'manual',
      message: 'Rate plan action must be selected for this rule.',
    });
    return false;
  }
  clearErrors('changeRatePlan');

  if (!checkRules) {
    setError('deActivateSim', {
      type: 'manual',
      message: 'Either deActivateSim or sendEmail must be selected',
    });
    setError('emailStatus', {
      type: 'manual',
      message: 'Either deActivateSim or emailStatus must be selected',
    });
    return false;
  }
  clearErrors('deActivateSim');
  clearErrors('emailStatus');

  return true;
};

export default validateCheckRules;
