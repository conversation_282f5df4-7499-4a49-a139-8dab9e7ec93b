import { Option } from 'user.model';

export interface RuleFormValues {
    ruleCategory: Option;
    ruleDefinition: Option;
    ruleName: string;
    ruleType: Option;
    dataVolume: number;
    volumeUnit: Option;
    status: boolean;
    deActivateSim: boolean;
    changeRatePlan: boolean;
    primaryEmail: string[];
    reActivateSIM: Option;
    source: { id: number; value: string } | null;
    ratePlanAction: Option;
    target: { id: number; value: string } | null;
    emailStatus: boolean;
    accountId: { id: number; value: string } | number[];
    lock: boolean;
  }

  export const getRuleFormDefaults = (isAdmin: boolean,
    personalAccount: any, accountDetail: any, accountInfo: any) => ({
    emailStatus: false,
    deActivateSim: false,
    changeRatePlan: false,
    lock: false,
     
    accountId: isAdmin
      ? personalAccount?.accountId
        ? [Number(personalAccount.accountId)]
        : accountDetail
      : [accountInfo?.id],
  });
