import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import LandingPage from './LandingPage';

const theme = createTheme();

describe('LandingPage', () => {
  it('should render the Tabs component with the correct initial tab', () => {
    render(
      <ThemeProvider theme={theme}>
        <BrowserRouter>
          <LandingPage />
        </BrowserRouter>
      </ThemeProvider>,
    );

    expect(screen.getByText('Automation Rules')).toBeInTheDocument();
    // expect(screen.getByText('Audit Trail Log')).toBeInTheDocument();
  });

  // it('should update URL search parameters when tabs are switched', () => {
  //   render(
  //     <ThemeProvider theme={theme}>
  //       <BrowserRouter>
  //         <LandingPage />
  //       </BrowserRouter>
  //     </ThemeProvider>,
  //   );

  //   const auditTrailTab = screen.getByText('Audit Trail Log');
  //   fireEvent.click(auditTrailTab);

  //   expect(window.location.search).toBe('?tab=audit-log');
  // });
});
