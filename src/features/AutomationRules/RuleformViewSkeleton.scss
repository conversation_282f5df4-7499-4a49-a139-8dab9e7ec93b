.rule-form-list-skeleton {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  &__item {
    position: relative;
    width: 100%;
    height: 40px;
    border-radius: 4px;
    border: 1px solid var(--light-background, #f5f1fa);

    &::after {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      transform: translateX(-100%);
      background-image: linear-gradient(
        90deg,
        rgba($white-color, 0) 0,
        rgba($white-color, 0.2) 20%,
        rgba($white-color, 0.5) 60%,
        rgba($white-color, 0)
      );
      animation: shimmer 1s infinite;
      content: "";
    }

    @keyframes shimmer {
      100% {
        transform: translateX(100%);
      }
    }

    &_left {
      width: 200px;
      height: 190px;
      background: var(--light-background, #f5f1fa);
    }
  }
}

.rule-form-listView-skeleton {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  width: 100%;
  position: absolute;
  top: 90px;

  &__item {
    position: absolute;
    width: 99%;
    height: 40px;
    border-radius: 4px;
    border: 1px solid var(--light-background, #f5f1fa);
    overflow: hidden;

    &::after {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      transform: translateX(-100%);
      background-image: linear-gradient(
        90deg,
        rgba($white-color, 0) 0,
        rgba($white-color, 0.2) 20%,
        rgba($white-color, 0.5) 60%,
        rgba($white-color, 0)
      );
      animation: shimmer 1s infinite;
      content: "";
    }

    @keyframes shimmer {
      100% {
        transform: translateX(100%);
      }
    }

    &_left {
      width: 100%;
      height: 40px;
      background: var(--light-background, #f5f1fa);
    }
  }
}
