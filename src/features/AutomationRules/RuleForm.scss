.autocomplete-multiple-selected {
  .MuiAutocomplete-inputRoot {
    padding: 10px 70px 10px 10px !important;
  }
}

.rule-form-layout {
  svg {
    stroke: #000;
  }
}

.rule-form-laoder {
  width: 95% !important;
  padding: 15px;
}

.ratePlanPopper {
  width: 660px !important;
}

.source-ratePlan {
  padding: 0px 0px 0px 220px !important;
}

.target-ratePlanPopper {
  width: 500px !important;
}

.target-ratePlan {
  padding: 0px 50px 0px 0px !important;
}

.default {
  padding: 2px 8px;
  background-color: rgb(0, 97, 109);
  border-radius: 4px;
  color: white;
  font-size: 12px;
  margin-left: 15px;
}

.ruleFormScreen {
  overflow-y: auto;
  padding: 20px 20px 30px;
  margin-right: 4px;
  font-family: "Open Sans", sans-serif;

  .customBatch {
    margin-left: 18px;
  }

  .bottomBorder {
    border-bottom: 1px solid #e7e7ee;
  }

  .switchHead {
    border-bottom: 1px solid #e7e7ee;
    padding: 0px 15px 20px;
  }

  .switchLabelHead {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0px 0px;

    .switchLabel {
      color: #333;
      font-size: 14px;
      line-height: 19.6px;
    }
  }

  .MuiAutocomplete-endAdornment {
    top: 50% !important;
  }

  .MuiAutocomplete-root .MuiInputLabel-root {
    transform: translate(16px, 10px) scale(1);
  }

  .MuiAutocomplete-root .MuiInputLabel-root.MuiInputLabel-shrink {
    transform: translate(14px, -8px) scale(0.85) !important;
    background-color: #fff;
    padding: 0px 5px;
  }

  .textField .MuiInputLabel-root.MuiInputLabel-shrink {
    transform: translate(14px, -8px) scale(0.85) !important;
    background-color: #fff;
    padding: 0px 5px;
  }

  .lockUnlockule {
    background: #feeeec;
    padding: 5px 17px !important;
    margin-bottom: 20px;
    border-radius: 1;
    justify-content: flex-start !important;
    gap: 8px;
  }
}
