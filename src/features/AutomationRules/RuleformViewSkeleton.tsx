import React from 'react';
import './RuleformViewSkeleton.scss';

interface RuleformViewSkeletonProps {
  count: number;
  optionalClass?: string; // optionalClassal prop to add additional class
}

const RuleformViewSkeleton: React.FC<RuleformViewSkeletonProps> = ({ count, optionalClass = '' }) => (
  <div
    className={`rule-form-listView-skeleton ${optionalClass}`}
    data-testid="rule-form-listView-skeleton"
  >
    {[...Array(count)].map((_, i) => (
      <div
        key={i}
        className="rule-form-listView-skeleton__item"
        style={{ top: `${i * 70}px` }}
      >
        <div className="rule-form-listView-skeleton__item_left" />
      </div>
    ))}
  </div>
);

RuleformViewSkeleton.defaultProps = {
  optionalClass: '',
};

export default RuleformViewSkeleton;
