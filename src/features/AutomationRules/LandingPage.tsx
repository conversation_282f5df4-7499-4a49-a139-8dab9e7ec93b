import { Box, useTheme } from '@mui/material';
import TabPanel from '@nv2/nv2-pkg-js-shared-components/lib/TabPanel';
import Tabs from '@nv2/nv2-pkg-js-shared-components/lib/Tabs';
import CommonAuthwrapper from 'core/CommonAuthwrapper';
import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import FadeIn from 'shared/FadeIn';
import { PERMISSION, REPOSITORY } from 'features/constants';
import { useGetAuthorization } from 'PrivateRotes';
import AuditLog from './AuditLog';
import SIMAutomation from './AutomationRules';
import { ITab, SIMAUTOMATION_TAB, SIMAUTOMATION_TAB_INDEXES } from './LandingPage.models';

export const tabs: ITab = {
  [SIMAUTOMATION_TAB.AUTOMATION_RULES]: 0,
  [SIMAUTOMATION_TAB.GENERAL_AUDIT_TRAIL]: 1,
};

interface ILandingPageProps {
  isAdmin?: boolean;
}

const LandingPage = ({ isAdmin }: ILandingPageProps) => {
  const theme = useTheme();
  const [tabIndex, setTabIndex] = useState<number | undefined>(0);
  const [searchParams, setSearchParams] = useSearchParams();

  useEffect(() => {
    const tabFromQuery = searchParams.get('tab');
    if (tabFromQuery && tabs[tabFromQuery] !== tabIndex) {
      setTabIndex(tabs[tabFromQuery]);
      setSearchParams({ tab: tabFromQuery }, {
        replace: true,
      });
    } else if (!tabFromQuery) {
      setSearchParams({ tab: Object.keys(tabs)[SIMAUTOMATION_TAB_INDEXES.AUTOMATION_RULES] }, {
        replace: true,
      });
    }
  }, [searchParams]);

  const selectedNewTab = async (event, newTabValue) => {
    setTabIndex(newTabValue);
    setSearchParams({ tab: Object.keys(tabs)[newTabValue] }, {
      replace: true,
    });
  };
  const TabsValues = [
    {
      name: 'Automation Rules',
      disabled: !useGetAuthorization([PERMISSION.VIEW_RULE_DETAIL], REPOSITORY.SIM_AUTOMATION),
    },
    // {
    //   name: 'Audit Trail Log',
    // },
  ];
  const tabsConfig = {
    tabItemsConfig: TabsValues,
    primaryColor: theme.palette.secondary.main,
  };

  return (
    <Box
      className="LandingPage"
      sx={{
        backgroundColor: '#fff',
        padding: '20px',
        'span.tabs-tab-component__title': {
        },
        '.Mui-selected': {
          'span.tabs-tab-component__title': {
            color: '#1C1C28 !important',
          },
        },
        '.MuiTab-root:hover': {
          color: '#1C1C28 !important',
          'span.tabs-tab-component__title': {
            color: '#1C1C28 !important',
          },
        },
      }}
    >
      <Tabs
        tabIndex={tabIndex}
        setTabIndex={setTabIndex}
        selectedNewTab={selectedNewTab}
        selectedTab={tabIndex}
        {...tabsConfig}
      />
      <TabPanel index={SIMAUTOMATION_TAB_INDEXES.AUTOMATION_RULES} value={tabIndex}>
        <FadeIn>
          <CommonAuthwrapper
            permission={[PERMISSION?.VIEW_RULE_DETAIL]}
            repository={[REPOSITORY?.SIM_AUTOMATION]}
            isComponent
          >
            <SIMAutomation selectedTab={tabIndex} isAdmin={isAdmin ?? false} />
          </CommonAuthwrapper>
        </FadeIn>
      </TabPanel>
      <TabPanel index={SIMAUTOMATION_TAB_INDEXES.GENERAL_AUDIT_TRAIL} value={tabIndex}>
        <FadeIn>
          <CommonAuthwrapper
            permission={[]}
            repository={[]}
            isComponent
          >
            <AuditLog />
          </CommonAuthwrapper>
        </FadeIn>
      </TabPanel>
    </Box>
  );
};

LandingPage.defaultProps = {
  isAdmin: false,
};

export default LandingPage;
