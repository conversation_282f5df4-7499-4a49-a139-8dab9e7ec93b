import {
  MailOutlined,
  RobotOutlined,
} from '@ant-design/icons';
import {
  Box,
  IconButton,
  Stack,
  Switch,
  TableCell,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import { useAppContext } from 'AppContextProvider';
import CommonAuthwrapper from 'core/CommonAuthwrapper';
import commaSeperateNumber from 'core/utilities/commaSeperateNumber';
import { PERMISSION, REPOSITORY, TOOL_TIPS } from 'features/constants';
import React from 'react';
import {
  GrEdit, GrLock,
  GrUnlock,
  GrView,
} from 'react-icons/gr';
import Batch from 'shared/Batch/Batch';
import BooleanStatus from 'shared/BooleanStatus/BooleanStatus';
import Image from 'shared/LazyLoad/Image';
import { IAccountTableRow } from 'user.model';

export default function AccountTableRow({
  row, onEditClickHandler, onViewClickHandler,
  onLockClickHandler, view, setDialog, setUuid, setSelectedRuleStatus, setRuleName,
}: IAccountTableRow) {
  const { currentTheme } = useAppContext()
  console.log('currentTheme: ', currentTheme);
  return (
    <TableRow
      data-testid="table-row"
      sx={{
        ':hover': {
          cursor: 'pointer',
        },
      }}
      //   onClick={() => clickHandler(row.id)}
      key={row.uuid}
    >
      {view && (
        <TableCell sx={{ minWidth: '230px' }}>
          <Stack direction="row" spacing={1} marginLeft={3} alignItems="center" gap={5}>
            <Image
              src={row.logoUrl || ''}
              alt={row.ruleName}
              style={
                {
                  display: 'block',
                  maxWidth: '30px',
                  width: '57px',
                  height: 'auto',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                }
              }
            />
            {row?.accountName}
          </Stack>
        </TableCell>
      )}

      <TableCell sx={{ minWidth: '150px' }}>
        <Box
          component="div"
          sx={{ marginLeft: !view ? '20px' : '0px' }}
        >
          <Batch label={row?.ruleType} />
          <Tooltip title={row?.ruleCategory} arrow placement="top">
            <Typography variant="body2" sx={{ fontSize: '14px' }}>
              <span style={{ marginRight: '5px' }}>
                {row?.lock ? <GrLock /> : <GrUnlock />}
              </span>
              {row?.ruleCategory}
            </Typography>
          </Tooltip>
        </Box>
      </TableCell>

      <TableCell sx={{ minWidth: '150px' }}>
        {row?.ruleName}
      </TableCell>

      <TableCell sx={{ minWidth: '280px', padding: '0px 10px' }}>
        {row?.ruleDefinition}
      </TableCell>

      <TableCell sx={{ minWidth: '120px', maxWidth: '10px' }}>
        {`${commaSeperateNumber(row?.dataVolume)} ${row?.unit.includes('PERCENTAGE') ? '%' : row?.unit.replaceAll('_', " ")}`}
      </TableCell>

      <TableCell sx={{ minWidth: '150px' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
          <Tooltip
            disableHoverListener={Object.values(row?.action).every((action) => !action)}
            title={(
              <div>
                {row?.action.map((action, ind: number) => {
                  const actionDetails = Object.values(action)[0] as unknown as { name: string };
                  const uniqueKey = `${actionDetails.name}_${ind}`;
                  return (
                    <div key={uniqueKey}>{`${ind + 1}. ${actionDetails.name}`}</div>
                  );
                })}
              </div>
            )}
            arrow
            placement="top"
          >
            <IconButton
              size="medium"
              sx={{
                height: '46px',
                width: '66px',
                opacity: Object.values(row?.action).filter((action) => action).length ? 1 : 0.5,
                backgroundColor: currentTheme?.actionBg,
                ':hover': { color: currentTheme?.secondaryColor },
              }}
            >
              <RobotOutlined />
              {' '}
              <span style={{ marginLeft: '10px', fontSize: '14px', fontWeight: '700' }}>
                {row?.action?.filter((action) => action && Object.keys(action).length).length}
              </span>
            </IconButton>
          </Tooltip>
          <Tooltip
            disableHoverListener={!row?.notification?.email?.length}
            title="Email"
            arrow
            placement="top"
          >
            <IconButton
              size="medium"
              sx={{
                height: '46px',
                opacity: row?.notification?.email?.length ? 1 : 0.5,
                width: '66px',
                backgroundColor: currentTheme?.actionBg,
                ':hover': { color: currentTheme?.secondaryColor },
              }}
            >
              <MailOutlined />
              <span style={{ marginLeft: '10px', fontSize: '14px', fontWeight: '700' }}>
                {row?.notification?.email?.length}
              </span>
            </IconButton>
          </Tooltip>
        </Box>
      </TableCell>

      {/* <TableCell sx={{ minWidth: '220px' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
          <Tooltip
            disableHoverListener={!row?.notification?.email?.length}
            title="Email"
            arrow
            placement="top"
          >
            <IconButton
              size="medium"
              sx={{
                height: '46px',
                opacity: row?.notification?.email?.length ? 1 : 0.5,
                width: '66px',
                backgroundColor: '#ebe3f6',
                ':hover': { color: '#D200D6' },
              }}
            >
              <MailOutlined />
              <span style={{ marginLeft: '10px', fontSize: '14px', fontWeight: '700' }}>
                {row?.notification?.email?.length}
              </span>
            </IconButton>
          </Tooltip>

          <Tooltip
            disableHoverListener={!row?.notification?.sms?.length}
            title="SMS"
            arrow
            placement="top"
          >
            <IconButton
              size="medium"
              sx={{
                height: '46px',
                width: '66px',
                opacity: row?.notification?.sms?.length ? 1 : 0.5,
                backgroundColor: '#ebe3f6',
                ':hover': { color: '#D200D6' },
              }}
            >
              <MessageOutlined />
              <span style={{ marginLeft: '10px', fontSize: '14px', fontWeight: '700' }}>
                {row.notification?.sms?.length}
              </span>
            </IconButton>
          </Tooltip>

          <Tooltip
            disableHoverListener={!row?.notification?.push?.length}
            title={(
              <div>
                {row?.notification?.push?.map((action, ind: number) => (
                  // eslint-disable-next-line react/no-array-index-key
                  <div key={ind}>{`${ind + 1}. ${action}`}</div>
                ))}
              </div>
            )}
            arrow
            placement="top"
          >
            <IconButton
              size="medium"
              sx={{
                height: '46px',
                width: '66px',
                // marginLeft: '15px',
                backgroundColor: '#ebe3f6',
                ':hover': { color: '#D200D6' },
              }}
            >
              <NodeExpandOutlined />
              <span style={{ marginLeft: '10px', fontSize: '14px', fontWeight: '700' }}>
                {row?.notification?.push?.length}
              </span>
            </IconButton>
          </Tooltip>
        </Box>
      </TableCell> */}

      <TableCell sx={{
        display: 'flex',
        gap: 5,
        alignItems: 'center',
      }}
      >
        <CommonAuthwrapper permission={[PERMISSION?.UPDATE_RULE_STATUS]} repository={[REPOSITORY?.SIM_AUTOMATION]}>
          <Box sx={{
            display: 'flex', alignItems: 'center', gap: '15px', height: '60px',
          }}
          >
            <BooleanStatus status={row?.status} />
            <Switch
              defaultChecked={row?.status}
              checked={row?.status}
              disabled={row?.lock}
              onChange={() => {
                setSelectedRuleStatus(row?.status);
                setUuid(row?.uuid);
                setDialog(true);
                setRuleName(row?.ruleName);
              }}
              sx={{
                '.MuiSwitch-sizeMedium': {
                  cursor: row?.lock ? 'not-allowed' : '',
                },
                '.MuiSwitch-thumb': {
                  color: row?.lock && row?.status ? '#999999 !important' : '',
                },
                '.MuiSwitch-track': {
                  backgroundColor: row?.status && row?.lock ? '#DFDFE7 !important' : '',
                },
              }}
            />
          </Box>
        </CommonAuthwrapper>

        <CommonAuthwrapper
          permission={[PERMISSION?.VIEW_RULES, PERMISSION?.VIEW_RULE_DETAIL]}
          repository={[REPOSITORY?.SIM_AUTOMATION]}
        >
          <Box sx={{
            visibility: 'hidden',
          }}
          >
            <Tooltip title="View" arrow placement="top">
              <IconButton
                onClick={(e) => {
                  e.stopPropagation();
                  onViewClickHandler?.(row?.uuid);
                }}
                data-testid="viewButton"
                size="medium"
                className='black_stroke'
                sx={{ height: '44px', width: '44px', marginLeft: '15px' }}
              >
                <GrView size={22} />
              </IconButton>
            </Tooltip>
          </Box>
        </CommonAuthwrapper>

        <CommonAuthwrapper
          permission={[PERMISSION?.EDIT_RULE, PERMISSION?.VIEW_RULES, PERMISSION?.VIEW_RULE_DETAIL]}
          repository={[REPOSITORY?.SIM_AUTOMATION]}
        >
          <Box sx={{
            visibility: 'hidden',
            color: '#5514b4',
          }}
          >
            <Tooltip title={row?.lock ? TOOL_TIPS?.CANNOT_EDIT : 'Edit'} arrow placement="top">
              <IconButton
                onClick={(e) => {
                  if (row?.lock) return;
                  e.stopPropagation();
                  onEditClickHandler?.(row?.uuid);
                }}
                data-testid="editButton"
                size="medium"
                className='black_stroke'
                sx={{
                  height: '44px',
                  width: '44px',
                  opacity: row?.lock ? 0.5 : 1,
                  cursor: row?.lock ? 'not-allowed' : '',
                }}
              >
                <GrEdit size={22} />
              </IconButton>
            </Tooltip>
            {/* <Tooltip title="Navigate" arrow placement="top">
              <IconButton size="medium"><AiOutlineUserAdd /></IconButton>
            </Tooltip>
            <Tooltip title="Delete" arrow placement="top">
              <IconButton
                size="medium"
                onClick={(e) => {
                  onClickHandler(row.id);
                  e.stopPropagation();
                }}
              >
                <AiOutlineDelete />

              </IconButton>
            </Tooltip> */}

          </Box>
        </CommonAuthwrapper>

        <CommonAuthwrapper
          permission={[PERMISSION?.LOCK_RULE]}
          repository={[REPOSITORY?.SIM_AUTOMATION]}
        >
          <Box sx={{
            visibility: 'hidden',
            color: '#5514b4',
          }}
          >
            <Tooltip title={row?.lock ? 'Unlock' : 'Lock'} arrow placement="top">
              <IconButton
                size="medium"
                sx={{ height: '44px', width: '44px', marginRight: '15px' }}
                className='black_stroke'
                onClick={(e) => {
                  e.stopPropagation();
                  onLockClickHandler?.(row?.uuid, row?.lock, row?.ruleName);
                }}
              >
                {row?.lock ? <GrUnlock /> : <GrLock />}
              </IconButton>
            </Tooltip>
          </Box>
        </CommonAuthwrapper>
      </TableCell>
    </TableRow>
  );
}

AccountTableRow.defaultProps = {
  view: false,
};
