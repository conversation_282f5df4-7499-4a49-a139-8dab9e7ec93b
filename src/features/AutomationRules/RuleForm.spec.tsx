import React from 'react';
import {
    fireEvent,
    render, screen, waitFor,
} from '@testing-library/react';
import { toastError } from 'core/utilities/toastHelper';
import CommonDrawer from 'shared/Drawer/CommonDrawer';
import { BrowserRouter } from 'react-router-dom';
import { RuleFormProps } from 'user.model';
import RuleForm from './RuleForm';

// Mock the external dependencies
jest.mock('core/utilities/toastHelper', () => ({
    toastSuccess: jest.fn(),
    toastError: jest.fn(),
}));

jest.mock('./api.service', () => ({
    getCategory: jest.fn().mockResolvedValue({
        data: {
            results: [
                {
                    id: 1, category: 'Category 1', ruleTypeId: 1, ruleType: 'Type 1',
                },
            ],
        },
    }),
    getDefination: jest.fn().mockResolvedValue({
        data: {
            results: [
                { id: 1, definition: 'Definition 1' },
            ],
        },
    }),
    getRuleData: jest.fn(),
    ruleStatusbyId: jest.fn(),
    submitRule: jest.fn(),
    updateRuleData: jest.fn(),
}));

// Mock AppContextProvider
jest.mock('AppContextProvider', () => ({
    useAppContext: () => ({
        currentTheme: {
            primaryColor: '#5514B4',
            actionBg: '#ebe3f6',
        },
    }),
}));

// Mock react-hook-form
jest.mock('react-hook-form', () => ({
    ...jest.requireActual('react-hook-form'),
    useForm: () => ({
        control: {},
        handleSubmit: jest.fn((fn) => fn),
        formState: { errors: {} },
        watch: jest.fn(),
        setValue: jest.fn(),
        clearErrors: jest.fn(),
        setError: jest.fn(),
        reset: jest.fn(),
    }),
    Controller: ({ render }: any) => render({ field: { onChange: jest.fn(), value: '' }, fieldState: { error: null } }),
}));

const mockProps: RuleFormProps = {
    units: { KB: [{ ref: 'KB', value: 'KB' }], MB: [{ ref: 'MB', value: 'MB' }] },
    ruleAction: [{ ref: 'REACTIVATE_SIM', value: 'Reactivate Sim' }],
    ratePlansActions: [{ ref: 'CHANGE_RATE_PLAN', value: 'Change Rate Plan' }],
    onClose: jest.fn(),
    ruleId: '',
    onSave: jest.fn(),
    setStatusToggle: jest.fn(),
    setStatusCounter: jest.fn(),
    setIsView: jest.fn(),
    selectedEmail: ['<EMAIL>'],
    view: false,
    create: false,
    isAdmin: true,
    accountInfo: {
        id: 1,
        name: 'Test Account',
    },
    personalAccount: {
        accountId: 1,
        isAdmin: false,
    },
};

describe('RuleForm Component', () => {
    test('renders RuleForm component', async () => {
        render(
          <BrowserRouter>
            <CommonDrawer
              anchor="right"
              open
              onClose={() => jest.fn()}
              heading="test"
              variant="temporary"
            >
              <RuleForm {...mockProps} />
            </CommonDrawer>
          </BrowserRouter>,
        );
        waitFor(() => expect(screen.getByText('Rule Details')).toBeInTheDocument());
    });

    test('validates required fields', async () => {
        render(<BrowserRouter><RuleForm {...mockProps} /></BrowserRouter>);
        waitFor(() => (screen.getByText('Save Changes')));
        waitFor(() => expect(screen.getByText('Save Changes')).toBeInTheDocument());
        waitFor(() => fireEvent.click(screen.getByText('Save Changes')));
        waitFor(() => {
            expect(screen.getByText('Rule Category required')).toBeInTheDocument();
            expect(screen.getByText('Rule Name required')).toBeInTheDocument();
            expect(screen.getByText('Data Volume required')).toBeInTheDocument();
            expect(screen.getByText('Volume Unit required')).toBeInTheDocument();
        });
    });

    test('handles API errors gracefully', async () => {
        const mockError = new Error('API Error');
        const { getRuleData } = jest.requireMock('./api.service');
        getRuleData.mockRejectedValueOnce(mockError);

        render(<BrowserRouter><RuleForm {...mockProps} ruleId="1" /></BrowserRouter>);

        await waitFor(() => {
            expect(toastError).toHaveBeenCalledWith('Something went wrong');
            expect(mockProps.onClose).toHaveBeenCalled();
        });
    });

    test('renders in view mode', () => {
        render(
            <BrowserRouter>
                <RuleForm {...mockProps} view={true} />
            </BrowserRouter>,
        );
        expect(screen.getByText('Rule Details')).toBeInTheDocument();
    });

    test('renders in create mode', () => {
        render(
            <BrowserRouter>
                <RuleForm {...mockProps} create={true} />
            </BrowserRouter>,
        );
        expect(screen.getByText('Rule Details')).toBeInTheDocument();
    });

    test('handles form cancellation', () => {
        render(
            <BrowserRouter>
                <RuleForm {...mockProps} />
            </BrowserRouter>,
        );
        const cancelButton = screen.getByText('Cancel');
        fireEvent.click(cancelButton);
        expect(mockProps.onClose).toHaveBeenCalled();
    });

    test('handles different unit types', () => {
        const propsWithDifferentUnits = {
            ...mockProps,
            units: {
                GB: [{ ref: 'GB', value: 'GB' }],
                TB: [{ ref: 'TB', value: 'TB' }]
            }
        };

        render(
            <BrowserRouter>
                <RuleForm {...propsWithDifferentUnits} />
            </BrowserRouter>,
        );
        expect(screen.getByText('Rule Details')).toBeInTheDocument();
    });

    test('handles empty units', () => {
        render(
            <BrowserRouter>
                <RuleForm {...mockProps} units={{}} />
            </BrowserRouter>,
        );
        expect(screen.getByText('Rule Details')).toBeInTheDocument();
    });

    test('handles empty rule actions', () => {
        render(
            <BrowserRouter>
                <RuleForm {...mockProps} ruleAction={[]} />
            </BrowserRouter>,
        );
        expect(screen.getByText('Rule Details')).toBeInTheDocument();
    });

    test('handles empty rate plan actions', () => {
        render(
            <BrowserRouter>
                <RuleForm {...mockProps} ratePlansActions={[]} />
            </BrowserRouter>,
        );
        expect(screen.getByText('Rule Details')).toBeInTheDocument();
    });

    test('handles personal account data', () => {
        const personalAccount = {
            accountId: 'test-account-id',
            isAdmin: true,
            ruleId: 'test-rule-id'
        };

        render(
            <BrowserRouter>
                <RuleForm {...mockProps} personalAccount={personalAccount} />
            </BrowserRouter>,
        );
        expect(screen.getByText('Rule Details')).toBeInTheDocument();
    });

    test('handles multiple selected emails', () => {
        render(
            <BrowserRouter>
                <RuleForm {...mockProps} selectedEmail={['<EMAIL>', '<EMAIL>']} />
            </BrowserRouter>,
        );
        expect(screen.getByText('Rule Details')).toBeInTheDocument();
    });

    test('handles empty selected emails', () => {
        render(
            <BrowserRouter>
                <RuleForm {...mockProps} selectedEmail={[]} />
            </BrowserRouter>,
        );
        expect(screen.getByText('Rule Details')).toBeInTheDocument();
    });

    test('handles form submission', async () => {
        render(
            <BrowserRouter>
                <RuleForm {...mockProps} />
            </BrowserRouter>,
        );
        const saveButton = screen.getByText('Save Changes');
        fireEvent.click(saveButton);
        expect(mockProps.onSave).toHaveBeenCalled();
    });
});
