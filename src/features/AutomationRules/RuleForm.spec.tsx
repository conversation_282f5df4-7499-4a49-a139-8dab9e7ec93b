import React from 'react';
import {
    fireEvent,
    render, screen, waitFor,
} from '@testing-library/react';
import { toastError } from 'core/utilities/toastHelper';
import CommonDrawer from 'shared/Drawer/CommonDrawer';
import { BrowserRouter } from 'react-router-dom';
import { RuleFormProps } from 'user.model';
import RuleForm from './RuleForm';

// Mock the external dependencies
jest.mock('core/utilities/toastHelper', () => ({
    toastSuccess: jest.fn(),
    toastError: jest.fn(),
}));

jest.mock('./api.service', () => ({
    getCategory: jest.fn().mockResolvedValue({
        data: {
            results: [
                {
                    id: 1, category: 'Category 1', ruleTypeId: 1, ruleType: 'Type 1',
                },
            ],
        },
    }),
    getDefination: jest.fn().mockResolvedValue({
        data: {
            results: [
                { id: 1, definition: 'Definition 1' },
            ],
        },
    }),
    getRuleData: jest.fn(),
    ruleStatusbyId: jest.fn(),
    submitRule: jest.fn(),
    updateRuleData: jest.fn(),
}));

const mockProps: RuleFormProps = {
    units: [{ id: 1, value: 'MB' }],
    ruleAction: [{ id: 1, value: 'Reactivate Sim' }],
    ratePlansActions: [{ id: 1, value: 'Reactivate Sim' }],
    onClose: jest.fn(),
    ruleId: '',
    onSave: jest.fn(),
    setStatusToggle: jest.fn(),
    setStatusCounter: jest.fn(),
    setIsView: jest.fn(),
    selectedEmail: ['<EMAIL>'],
    view: false,
    accountInfo: {
        id: 1,
        name: 'Test Account',
    },
    personalAccount: {
        accountId: 1,
        isAdmin: false,
    },
};

describe('RuleForm Component', () => {
    test('renders RuleForm component', async () => {
        render(
          <BrowserRouter>
            <CommonDrawer
              anchor="right"
              open
              onClose={() => jest.fn()}
              heading="test"
              variant="temporary"
            >
              <RuleForm {...mockProps} />
            </CommonDrawer>
          </BrowserRouter>,
        );
        waitFor(() => expect(screen.getByText('Rule Details')).toBeInTheDocument());
    });

    test('validates required fields', async () => {
        render(<BrowserRouter><RuleForm {...mockProps} /></BrowserRouter>);
        waitFor(() => (screen.getByText('Save Changes')));
        waitFor(() => expect(screen.getByText('Save Changes')).toBeInTheDocument());
        waitFor(() => fireEvent.click(screen.getByText('Save Changes')));
        waitFor(() => {
            expect(screen.getByText('Rule Category required')).toBeInTheDocument();
            expect(screen.getByText('Rule Name required')).toBeInTheDocument();
            expect(screen.getByText('Data Volume required')).toBeInTheDocument();
            expect(screen.getByText('Volume Unit required')).toBeInTheDocument();
        });
    });

    test('handles API errors gracefully', async () => {
        const mockError = new Error('API Error');
        const { getRuleData } = jest.requireMock('./api.service');
        getRuleData.mockRejectedValueOnce(mockError);

        render(<BrowserRouter><RuleForm {...mockProps} ruleId="1" /></BrowserRouter>);

        await waitFor(() => {
            expect(toastError).toHaveBeenCalledWith('Something went wrong');
            expect(mockProps.onClose).toHaveBeenCalled();
        });
    });
});
