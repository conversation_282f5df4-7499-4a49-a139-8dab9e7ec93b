import { RobotOutlined, UnorderedListOutlined } from '@ant-design/icons';
import {
  Box, Button, MenuItem, Pagination,
  PaginationItem,
  Select, Table, TableBody, TableRow,
  Typography,
} from '@mui/material';
import axios, { AxiosResponse } from 'axios';
import CommonAuthwrapper from 'core/CommonAuthwrapper';
import useAbortController from 'core/hooks/useAbortController';
import useMuiTableSearchParams from 'core/hooks/useMuiTableSearchParams';
import { toastError, toastSuccess } from 'core/utilities/toastHelper';
import { PERMISSION, REPOSITORY } from 'features/constants';
import React, { useContext, useEffect, useState } from 'react';
import {
  RxDoubleArrowLeft, RxDoubleArrowRight,
} from 'react-icons/rx';
import { SlArrowLeft, SlArrowRight } from 'react-icons/sl';
import { useLocation, useNavigate } from 'react-router-dom';

import Dialog from 'shared/Dialog/Dialog';
import RuleStatusDialog from 'shared/DialogConfirm/RuleStatusDialog';
import CommonDrawer from 'shared/Drawer/CommonDrawer';
import SimManagementPlaceholder from 'shared/EmptyPlaceHolder';
import SearchInput from 'shared/SearchInput';
import TopBar from 'shared/TopBar';
import { RouteContext } from 'SimAutomationRoute/SimAutomationRoutes';
import {
  IAccountInfo, simAutomation,
} from 'user.model';
import {
  getAccountDetails,
  getAccountId, getAccounts, getActions,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getUnits, deleteRule,
  lockStatusbyId,
  ruleStatusbyId,
} from './api.service';
import AccountTableRow from './AutomationTableRow';
import { StyledTableContainer } from './ComponentUipart';
import EnhancedTableHead, { sortFieldNames } from './EnhanchedTable';
import { SIMAUTOMATION_TAB_INDEXES } from './LandingPage.models';
import RuleForm from './RuleForm';
import RuleformViewSkeleton from './RuleformViewSkeleton';
import './AutomationRules.scss';
import { useAppContext } from 'AppContextProvider';

interface AutomationRulesProps {
  selectedTab: undefined | number,
  isAdmin: boolean,
}

const AutomationRules = ({ selectedTab, isAdmin }: AutomationRulesProps) => {
  const userorganizationId: any = useContext(RouteContext);
  const [isLoading, setIsLoading] = useState(false);
  const [simAutomations, setSimAutomations] = useState<simAutomation[]>([]);
  const [primaryEmail, setPrimaryEmail] = useState<string>('');
  const [accounts, setAccounts] = useState<any>();
  const [units, setUnits] = useState({});
  const [ruleAction, setRuleAction] = useState<any>([]);
  const [ratePlansActions, setRatePlansActions] = useState<any>([]);
  const [accountErrors, setAccountsError] = useState(false);
  const [searchValue, setSearchValue] = useState<string>('');
  const [heading, setHeading] = useState<string>('');
  const [ruleId, setRuleId] = useState<string>('');
  const [openDrawer, setOpenDrawer] = useState<boolean>(false);
  const [statusToggle, setStatusToggle] = useState<boolean>(false);
  const [statusCounter, setStatusCounter] = useState<number>(0);
  const [sortConfig, setSortConfig] = useState<any>({ key: null, direction: null });
  const [isView, setIsView] = useState<boolean>(false);
  const [isCreate, setIsCreate] = useState<boolean>(false);
  const [accountInfo, setAccountInfo] = useState<IAccountInfo>({
    id: 0,
    name: '',
  });
  const [dialog, setDialog] = React.useState(false);
  const [personalAccount, setPersonalAccount] = useState<any>({
    id: -1,
    value: 'loading...',
  });
  const [uuid, setUuid] = useState<string>('');
  const [selectedRuleStatus, setSelectedRuleStatus] = useState<boolean>(false);
  const [ruleName, setRuleName] = useState<string>('');
  const [lockDialog, setLockDialog] = useState(false);
  const [lockKey, setlockkey] = useState({
    id: '',
    lock: false,
    accountName: '',
  });
  const { cancelPreviousRequest, setNewController } = useAbortController();
  const location = useLocation();
  const { currentTheme } = useAppContext();
  const navigate = useNavigate();
  const cancelTokenSource = axios.CancelToken.source();
  const baseUrl = window.location.origin;

  useEffect(() => {
    if (location?.state) {
      setPersonalAccount(location?.state);
    }
  }, []);

  const organizationId = async () => {
    try {
      const { data } = await getAccountId(userorganizationId?.organization?.id);
      setAccountInfo(data);
      setPersonalAccount(data);

      if (data) {
        const Email = await getAccountDetails(data?.id);
        setPrimaryEmail(Email?.data?.email);
      }
    } catch (error: any) {
      toastError(error?.response?.data?.detail || 'Something went wrong');
    }
  };

  useEffect(() => {
    if (!isAdmin) {
      organizationId();
    }
  }, [userorganizationId?.organization?.id]);

  const {
    generateParamsForUrl,
    getParamsFromUrl,
  } = useMuiTableSearchParams();

  const {
    page, pageSize, field, sort, search,
  } = getParamsFromUrl();

  const transformActions = (actions: Record<string, any> | null) => {
    if (!actions) return [];

    return Object.entries(actions)
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      .filter(([_, value]) => value !== null)
      .map(([key, value]) => ({ [key]: value }));
  };

  const getAllAccounts = async (paramPage, paramPageSize, paramSearch, paramField?, paramSort?) => {
    const newSearchParams = generateParamsForUrl(paramPage, paramPageSize, paramField, paramSort, paramSearch);
    const newUrl = `${location.pathname.toString()}?tab=automation-rules&${newSearchParams.toString()}`;
    cancelPreviousRequest();
    const { signal } = setNewController();
    navigate(newUrl, { replace: true });
    try {
      setIsLoading(true);
      let ordering;
      if (paramField) { ordering = sortFieldNames[paramField]; }
      const accountsResponse
        : AxiosResponse = await getAccounts(paramPage, paramPageSize, ordering, paramSort, paramSearch, signal);
      const { data } = accountsResponse;
      setAccounts(data);
      const transformedData = data.results.map((item: any) => ({
        ...item,
        action: transformActions(item.action),
      }));
      setSimAutomations(transformedData);
      setIsLoading(false);
      setAccountsError(false);
    } catch (error) {
      if (axios.isCancel(error)) {
        toastError('Request was canceled');
        return;
      }
      setSimAutomations([]);
      setAccountsError(true);
      setIsLoading(false);
    }
  };

  const getStaticData = async () => {
    try {
      setIsLoading(true);
      const unit = await getUnits();

      const transformedUnits = {};

      for (const key in unit?.data) {
        transformedUnits[key] = unit?.data[key].map(({ ref, name }) => ({ ref, value: name.replaceAll("_", " ") }));
      }

      setUnits(transformedUnits);

      const action = await getActions();
      const allActions = action?.data?.reactivateSim?.map((result) => ({
        ref: result?.ref,
        value: result?.name,
      }));
      const ratePlansActionList = action?.data?.changeRatePlan?.map((result) => ({
        ref: result?.ref,
        value: result?.name,
      }));
      setRatePlansActions(ratePlansActionList);
      setRuleAction(allActions);
      setIsLoading(false);
    } catch (error: any) {
      setIsLoading(false);
      toastError(error?.response?.data?.detail);
    }
  };

  useEffect(() => {
    getAllAccounts(page, pageSize, search, field, sort);
    getStaticData();
    if (search) {
      setSearchValue(search);
    }
  }, []);

  useEffect(() => {
    if (selectedTab !== undefined && selectedTab !== SIMAUTOMATION_TAB_INDEXES.AUTOMATION_RULES) {
      return;
    }

    return () => {
      cancelTokenSource.cancel('Request canceled');
    };
  }, []);

  const createRulePlan = () => {
    setRuleId('');
    setOpenDrawer(true);
    setHeading('Create Rule');
    setIsCreate(true);
    setIsView(false);
  };

  const editRulePlan = (id: string) => {
    setRuleId(id);
    setOpenDrawer(true);
    setHeading('Edit Rule');
    setIsView(false);
    setIsCreate(false);

    // only for developer delete purpose uncomment to delete
    // deleteRule(id);
  };

  const viewRulePlan = (id: string) => {
    setRuleId(id);
    setOpenDrawer(true);
    setHeading('View Rule');
    setIsView(true);
    setIsCreate(false);
  };

  const lockRulePlan = (id: string, lock: boolean, accountName: string) => {
    setlockkey({ id, lock, accountName });
    setLockDialog(true);
  };

  const lockunlockRulePlan = async (keys) => {
    try {
      const ruleStatus = await lockStatusbyId(keys?.id, !keys?.lock);
      setSimAutomations(
        (prevItems) => prevItems?.map((item) => (item?.uuid === keys?.id ? { ...item, lock: !keys?.lock } : item),
        ),
      );
      setLockDialog(false);
      if (ruleStatus?.data) {
        toastSuccess(`Rule ${keys?.lock ? 'unlocked' : 'locked'} successfully  for ${keys?.accountName}.`);
      }
    } catch (error: any) {
      setLockDialog(false);
      toastError(error?.response?.data?.detail || 'Failed to update Rule Status. Please, try again!');
    }
  };

  useEffect(() => {
    if (personalAccount?.accountId && personalAccount?.isAdmin) {
      createRulePlan();
    } else if (personalAccount?.ruleId && Object.keys(units).length > 0) {
      editRulePlan(personalAccount?.ruleId);
    }
  }, [personalAccount, units]);

  const onCloseDrawer = () => {
    setOpenDrawer(false);
    setPersonalAccount(null);
    if (personalAccount?.accountId) {
      const queryParams = new URLSearchParams({
        tab: 'automation-rules',
        page: '1',
        pageSize: '10',
      });
      const path = `/accounts/${personalAccount?.accountId}?${queryParams.toString()}`;
      navigate(path, { state: null });
    }
  };

  const handleChange = (event) => {
    const { value } = event.target;
    let paramPage = parseInt(page.toString(), 10);
    const paramPageSize = parseInt(pageSize.toString(), 10);
    const totalRecords = accounts?.totalCount ? accounts?.totalCount : 1;
    if (paramPage === accounts?.lastPage && paramPageSize < value) {
      paramPage = Math.ceil(totalRecords / value);
    }
    getAllAccounts(paramPage, value, search, field, sort);
  };

  const handleSearch = (searchTerm: string) => {
    setSearchValue(searchTerm);
    getAllAccounts(1, pageSize, searchTerm, field, sort);
  };

  const handleChangePage = (event, pageValue) => {
    getAllAccounts(pageValue, pageSize, search, field, sort);
  };

  const updateStatus = async (id: string, status: boolean) => {
    try {
      const ruleStatus = await ruleStatusbyId(id, status);
      setSimAutomations(
        (prevItems) => prevItems?.map((item) => (item?.uuid === id ? { ...item, status } : item),
        ),
      );
      setDialog(false);
      if (ruleStatus?.data) {
        toastSuccess('Rule Status updated successfully!');
      }
    } catch (error: any) {
      setDialog(false);
      toastError(error?.response?.data?.detail || 'Failed to update Rule Status. Please, try again!');
    }
  };

  useEffect(() => {
    if (ruleId) {
      setSimAutomations(
        (prevItems) => prevItems?.map((item) => (item?.uuid === ruleId ? { ...item, status: !statusToggle } : item),
        ),
      );
    }
  }, [statusCounter]);

  const pageSizeInt = parseInt(pageSize.toString(), 10);
  const pageInt = parseInt(page.toString(), 10);
  const displayCount = accounts?.totalCount <= (pageInt * pageSizeInt) ? accounts?.totalCount : (pageInt * pageSizeInt);
  const pageIntStart = pageInt === 1 ? 1 : ((pageInt * pageSizeInt) - 9);

  return (
    <Box className="AutomationRules" data-testid="AutomationRules">
      <TopBar
        className="automation-top-bar"
        navigateTo={baseUrl}
        buttonIcon={<RobotOutlined />}
        buttonText="Create Rule"
        buttonAction={createRulePlan}
        repositories={[REPOSITORY?.SIM_AUTOMATION]}
        permissions={[PERMISSION?.CREATE_RULE]}
      >
        <div key={searchValue}>
          <SearchInput
            placeholder="Search"
            value={searchValue}
            onChange={(e: any) => handleSearch(e)}
          />
        </div>
      </TopBar>

      <StyledTableContainer className="scrollBar">
        <Table
          aria-label="simple table"
          stickyHeader
        >
          <EnhancedTableHead
            view={isAdmin}
            order={sortConfig.direction}
            isLoading={isLoading}
            orderBy={sortConfig.key}
            onRequestSort={(eventData, key) => {
              if (sortConfig.key === key) {
                if (sortConfig.direction === 'asc') {
                  getAllAccounts(page, pageSize, search, key, 'desc');
                  setSortConfig({ key, direction: 'desc' });
                } else if (sortConfig.direction === 'desc') {
                  getAllAccounts(page, pageSize, search);
                  setSortConfig({ key: null, direction: null });
                }
              } else {
                setSortConfig({ key, direction: 'asc' });
                getAllAccounts(page, pageSize, search, key, 'asc');
              }
            }}
          />
          {isLoading ? (
            <TableBody sx={{ height: '65vh' }}>
              <TableRow
                className="loader-row"
                sx={{
                  position: 'relative',
                  '.rule-form-listView-skeleton': {
                    top: 0,
                  },
                }}
              >
                <RuleformViewSkeleton count={8} />
              </TableRow>
            </TableBody>
          ) : (
            <TableBody>
              {
                simAutomations.map((row) => (
                  <AccountTableRow
                    view={isAdmin}
                    onEditClickHandler={editRulePlan}
                    onViewClickHandler={viewRulePlan}
                    onLockClickHandler={lockRulePlan}
                    row={row}
                    key={row.uuid}
                    onStatusChange={updateStatus}
                    setUuid={setUuid}
                    setSelectedRuleStatus={setSelectedRuleStatus}
                    setDialog={setDialog}
                    setRuleName={setRuleName}
                  />
                ))
              }
            </TableBody>
          )}
        </Table>
        {(!isLoading && (accountErrors || simAutomations?.length === 0)) && (
          <Box display="flex" alignItems="center" justifyContent="center" height="65vh">
            <SimManagementPlaceholder
              title="No Automation Rules Configured."
              text="You can create a new automation rule by clicking on 'Create Rule' button."
              icon={(
                <UnorderedListOutlined style={{
                  fontSize: '30px', color: currentTheme?.primaryColor || '#5514B4',
                }}
                />
              )}
              loading={false}
            />
          </Box>

        )}

      </StyledTableContainer>

      <Box
        display={(!isLoading && (accountErrors || simAutomations?.length === 0)) ? 'none' : 'flex'}
        alignItems="center"
        justifyContent="space-between"
        marginTop="8px"
        sx={{
          backgroundColor: currentTheme?.bgLightPrimary || '#f5f1fa',
          padding: '7px 10px',
          borderRadius: '4px',
          '& .MuiButtonBase-root, &.MuiPaginationItem-root': {
            borderRadius: '4px',
            minWidth: '40px',
            minHeight: '40px',
            padding: '4px 10px',
            fontWeight: 700,
            '&.Mui-selected': {
              color: '#5514b4',
              '&:hover': {
                backgroundColor: currentTheme?.bgLightMediumPrimay || '#ccb9e9',
              },
              backgroundColor: currentTheme?.bgLightMediumPrimay || '#ccb9e9',
            },
            '&:hover': {
              backgroundColor: currentTheme?.actionBg || '#ebe3f6',
            },

          },
        }}
      >

        <Box display="flex" alignItems="center" gap={3.5}>
          <Typography>Rows per page:</Typography>
          <Select
            labelId="demo-simple-select-label"
            id="demo-simple-select"
            value={pageSize}
            onChange={handleChange}
          >
            <MenuItem value={10}>10</MenuItem>
            <MenuItem value={20}>20</MenuItem>
            <MenuItem value={30}>30</MenuItem>
            <MenuItem value={40}>40</MenuItem>
            <MenuItem value={50}>50</MenuItem>
          </Select>
        </Box>
        <Typography>
          {`${pageIntStart} - ${displayCount} of ${accounts?.totalCount || 0}`}
        </Typography>
        <Pagination
          showFirstButton
          showLastButton
          count={accounts?.lastPage || 0}
          defaultPage={page ? parseInt(page.toString(), 10) : 1}
          renderItem={(item: any) => (
            <PaginationItem
              {...item}
              components={{
                first: () => <RxDoubleArrowLeft size={20} />,
                previous: () => <SlArrowLeft size={12} />,
                next: () => <SlArrowRight size={12} />,
                last: () => <RxDoubleArrowRight size={20} />,
              }}
            />
          )}
          siblingCount={0}
          boundaryCount={2}
          onChange={handleChangePage}
        />
      </Box>
      <CommonDrawer
        anchor="right"
        open={openDrawer}
        onClose={() => {
          onCloseDrawer();
        }}
        heading={heading}
        variant="temporary"
      >
        <CommonAuthwrapper
          permission={[PERMISSION?.EDIT_RULE, PERMISSION?.VIEW_RULES]}
          repository={[REPOSITORY?.SIM_AUTOMATION]}
        >
          <RuleForm
            accountInfo={accountInfo}
            view={isView}
            create={isCreate}
            isAdmin={isAdmin}
            units={units}
            ruleId={ruleId}
            ruleAction={ruleAction}
            ratePlansActions={ratePlansActions}
            setIsView={setIsView}
            onClose={() => {
              onCloseDrawer();
            }}
            onSave={() => getAllAccounts(page, pageSize, searchValue, field, sort)}
            setStatusToggle={(prev: boolean) => setStatusToggle(!prev)}
            setStatusCounter={() => setStatusCounter((prev: number) => prev + 1)}
            selectedEmail={primaryEmail === '' ? [] : [primaryEmail]}
            personalAccount={personalAccount}
          />
        </CommonAuthwrapper>
      </CommonDrawer>
      <RuleStatusDialog
        open={dialog}
        onSuccess={() => updateStatus?.(uuid, !selectedRuleStatus)}
        onClose={() => setDialog(false)}
        ruleName={ruleName}
        ruleStatus={selectedRuleStatus}
      />
      <Dialog
        open={lockDialog}
        onClose={() => setLockDialog(false)}
        title={`${lockKey?.lock ? 'Unlock' : 'Lock'} Rule`}
        className="lock-rule-dialog"
        footerchildren={(
          <Box
            display="flex"
            alignItems="flex-start"
            gap="15px"
            justifyContent="space-between"
          >
            <Button
              sx={{ p: '0px 25px' }}
              variant="contained"
              color="primary"
              onClick={() => lockunlockRulePlan(lockKey)}
            >
              Confirm
            </Button>

            <Button
              sx={{ p: '0px 25px', backgroundColor: currentTheme?.actionBg || '#ebe3f6', border: '0px' }}
              variant="outlined"
              onClick={() => setLockDialog(false)}
            >
              Cancel
            </Button>
          </Box>
        )}
      >
        <Typography component="div" variant="subtitle1" sx={{ mb: 4 }}>
          Are you sure you want to
          {lockKey?.lock ? ' unlock' : ' lock'}
          <Typography component="span" variant="subtitle1" sx={{ mx: 2, fontWeight: 'bold' }}>
            {lockKey?.accountName}
          </Typography>
          rule?
        </Typography>
      </Dialog>
    </Box>
  );
};

export default AutomationRules;
