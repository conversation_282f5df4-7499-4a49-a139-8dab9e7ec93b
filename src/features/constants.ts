export const REPOSITORY = {
  SIM_MANAGEMENT: 'SIM Details',
  MARKETSHAREREPORT: 'Reporting',
  RATEPLAN: 'Rate Plans',
  ACCOUNT_MANAGEMENT: 'Accounts',
  SIM_AUTOMATION: 'SIM Automation',
};

export const PERMISSION = {
  UPDATE_RULE_STATUS: 'Update Rule Status',
  VIEW_RULES: 'View Rules',
  EDIT_RULE: 'Edit Rule',
  VIEW_RULE_DETAIL: 'View Rule Detail',
  CREATE_RULE: 'Create Rule',
  LOCK_RULE: 'Lock Rule',
};

export const RATEPLAN_MODEL_TO_SHOW = ['FIXED', 'FLEXI'];

export const ROUTE_PERMISSION = {
  ratePlansList: 'View Rate Plans',
  ratePlansClientList: 'View Client Rate Plans',
  ratePlan: 'View Rate Plans',
  ratePlanEdit: 'Modify Rate Plan',
  ratePlanView: 'View Rate Plan',
  ratePlanCreate: 'Create Rate Plan',
  ratePlanNotFound: '',
  // Account
  GET_ACCOUNT_NAMES: 'View Account Names',
};

export const TOOL_TIPS = {
  LOCKED_RULE:
    'If you unlock this rule, the account user will be able to edit the current rule until you lock it',
  UNLOCKED_RULED:
    'If you lock this rule, the account user will not be able to edit the current rule until you unlock it',
  CANNOT_EDIT: "You can't edit lock rule",
  SIM_LIMIT:
    'The rate plan has a SIM limit, excess SIMs won’t be assigned once it’s reached.',
  EMAIL_TOOLTIP:
    'You can leave the email field empty, or add one or more email addresses that will receive notifications about exceeding the limit of SIM(s).',
  ACTIVATE_TOOLTIP: 'If the rule is activated, it will apply to all assigned SIMs. If deactivated, no further automatic actions will be performed for those SIMs.',
};

export const BACKUP_UNITS = [
  {
    ref: 'BYTES',
    value: 'BYTES',
  },
  {
    ref: 'KB',
    value: 'KB',
  },
  {
    ref: 'MB',
    value: 'MB',
  },
  {
    ref: 'GB',
    value: 'GB',
  },
  {
    ref: 'TB',
    value: 'TB',
  },
  {
    ref: 'PB',
    value: 'PB',
  },
];
