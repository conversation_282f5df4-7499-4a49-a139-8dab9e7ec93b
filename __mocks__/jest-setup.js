import '@testing-library/jest-dom';
import { TextEncoder, TextDecoder } from 'util'

beforeAll(() => {
  global.matchMedia = jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  }));
});

global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

window.matchMedia = window.matchMedia || function() {
  return {
    matches: false,
    addListener() {},
    removeListener() {},
  };
};
