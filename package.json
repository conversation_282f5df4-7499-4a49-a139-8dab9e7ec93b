{"name": "sim-automation", "version": "0.0.1", "private": true, "dependencies": {"@ant-design/icons": "^6.0.0", "@craco/craco": "^6.4.5", "@material-ui/core": "^4.12.4", "@mui/icons-material": "^5.17.1", "@mui/lab": "^5.0.0-alpha.176", "@mui/material": "^5.17.1", "@mui/styles": "^5.17.1", "@mui/utils": "^5.17.1", "@nv2/nv2-pkg-js-shared-components": "^2.32.0", "@nv2/nv2-pkg-js-theme": "^2.12.0", "@testing-library/react-hooks": "^8.0.1", "ajv": "^8.17.1", "axios": "^1.8.4", "core-js": "^2.6.12", "craco-sass-resources-loader": "^1.1.0", "formik": "^2.2.9", "jest-junit": "^16.0.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-cookie": "^4.1.1", "react-dom": "^18.2.0", "react-hook-form": "^7.42.1", "react-icons": "^4.7.1", "react-number-format": "^5.1.3", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-toastify": "^9.1.3", "ts-jest": "^29.0.3", "typescript": "^5.8.3"}, "scripts": {"start": "GENERATE_SOURCEMAP=false PORT=3006 craco start", "start:on-windows": "set GENERATE_SOURCEMAP=false && set PORT=3002 && craco start", "build": "craco build", "lint": "eslint src", "stylelint": "stylelint \"**/*.scss\"", "test": "jest --maxWorkers=10% --config ./jest.config.js --collectCoverage", "test:coverage": "CI=true npm test -- --env=jsdom --coverage", "eject": "react-scripts eject", "prepare": "husky install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.19.0", "@babel/plugin-proposal-export-default-from": "^7.18.10", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-react": "^7.18.6", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.2.4", "@types/node": "^22.14.0", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.9", "@types/react-slick": "^0.23.10", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "axios-mock-adapter": "^2.1.0", "eslint": "^9.24.0", "eslint-config-airbnb": "^19.0.4", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.1.2", "jest-css-modules-transform": "^4.4.2", "jest-environment-jsdom": "^29.0.3", "stylelint": "^14.16.1", "stylelint-config-standard-scss": "^5.0.0", "stylelint-scss": "^4.7.0"}}