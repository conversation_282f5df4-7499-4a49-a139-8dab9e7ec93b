import babelParser from '@babel/eslint-parser';
import reactPlugin from 'eslint-plugin-react';
import importPlugin from 'eslint-plugin-import';
import tsPlugin from '@typescript-eslint/eslint-plugin';  // Add TypeScript plugin
import tsParser from '@typescript-eslint/parser';  // Add TypeScript parser
import js from '@eslint/js';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import { FlatCompat } from '@eslint/eslintrc';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

export default [
  {
    files: ['**/*.js', '**/*.jsx'],
    ignores: ["build/**/*", "coverage/**/*", "node_modules/**", "__mocks__", "public/**"],
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: 'module',
      parser: babelParser,
      globals: {
        window: 'readonly',
        document: 'readonly',
        localStorage: 'readonly',
        FormData: 'readonly',
        FileReader: 'readonly',
        Blob: 'readonly',
        API_URL: 'readonly',
        navigator: 'readonly',
        JSX: true,
      },
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    plugins: {
      react: reactPlugin,
      import: importPlugin,
    },
    rules: {
      'default-param-last': 'off',
      'import/extensions': 'off',
      'import/no-named-as-default': 'off',
      'function-paren-newline': 'off',
      'function-call-argument-newline': 'off',
      'linebreak-style': 'off',
      "no-unused-vars": ["warn", { "varsIgnorePattern": "^React$" }],
      'jsx-a11y/label-has-for': 'off',
      'jsx-a11y/label-has-associated-control': 'off',
      'react/require-default-props': 'off',
      "no-console": ["warn", { "allow": ["clear", "error"] }],
      'react/jsx-filename-extension': ['warn', { extensions: ['.js', '.jsx'] }],
      'react/jsx-uses-vars': 'error',
      'no-underscore-dangle': 'off',
      'import/imports-first': ['error', 'absolute-first'],
      'import/newline-after-import': 'error',
      'import/no-unresolved': 'off',
      'import/no-extraneous-dependencies': ['error', { devDependencies: true }],
      'react/jsx-props-no-spreading': 'off',
      'max-len': ['warn', {
        code: 120,
        ignoreComments: true,
        ignoreUrls: true,
        ignoreStrings: true,
        ignoreTemplateLiterals: true,
        ignoreRegExpLiterals: true,
      }],
      'no-plusplus': 'off',
      'import/prefer-default-export': 'off',
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
  },
  {
    files: ['**/*.ts', '**/*.tsx'], // Separate TypeScript-specific configuration
    ignores: ["build/**/*", "coverage/**/*", "node_modules/**", "__mocks__", "public/**"],
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: 'module',
      parser: tsParser,
      parserOptions: {
        project: './tsconfig.json',
      },
    },
    plugins: {
      '@typescript-eslint': tsPlugin, // Add TypeScript ESLint plugin
    },
    rules: {
      '@typescript-eslint/no-unused-vars': ['warn'],
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-empty-function': 'warn',
    },
  },
];
